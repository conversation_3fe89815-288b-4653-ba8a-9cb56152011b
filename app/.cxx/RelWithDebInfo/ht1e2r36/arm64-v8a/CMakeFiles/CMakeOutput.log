The target system is: Android - 1 - aarch64
The host system is: Linux - 5.15.0-139-generic - x86_64
Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler: /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/bin/clang 
Build flags: -g;-DANDROID;-fdata-sections;-ffunction-sections;-funwind-tables;-fstack-protector-strong;-no-canonical-prefixes;-D_FORTIFY_SOURCE=2;-Wformat;-Werror=format-security;
Id flags: -c;--target=aarch64-none-linux-android29 

The output was:
0


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.o"

The C compiler identification is Clang, found in "/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/.cxx/RelWithDebInfo/ht1e2r36/arm64-v8a/CMakeFiles/3.22.1-g37088a8/CompilerIdC/CMakeCCompilerId.o"

Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ 
Build flags: -g;-DANDROID;-fdata-sections;-ffunction-sections;-funwind-tables;-fstack-protector-strong;-no-canonical-prefixes;-D_FORTIFY_SOURCE=2;-Wformat;-Werror=format-security;;-std=c++11
Id flags: -c;--target=aarch64-none-linux-android29 

The output was:
0


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.o"

The CXX compiler identification is Clang, found in "/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/.cxx/RelWithDebInfo/ht1e2r36/arm64-v8a/CMakeFiles/3.22.1-g37088a8/CompilerIdCXX/CMakeCXXCompilerId.o"

Detecting C compiler ABI info compiled with the following output:
Change Dir: /media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/.cxx/RelWithDebInfo/ht1e2r36/arm64-v8a/CMakeFiles/CMakeTmp

Run Build Command(s):/opt/sdk/cmake/3.22.1/bin/ninja cmTC_2712e && [1/2] Building C object CMakeFiles/cmTC_2712e.dir/CMakeCCompilerABI.c.o
Android (8481493, based on r416183c2) clang version 12.0.9 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)
Target: aarch64-none-linux-android29
Thread model: posix
InstalledDir: /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/bin
Found candidate GCC installation: /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib/gcc/aarch64-linux-android/4.9.x
Selected GCC installation: /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib/gcc/aarch64-linux-android/4.9.x
Candidate multilib: .;@m64
Selected multilib: .;@m64
 (in-process)
 "/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/bin/clang" -cc1 -triple aarch64-none-linux-android29 -emit-obj -mrelax-all --mrelax-relocations -mnoexecstack -disable-free -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=non-leaf -fno-rounding-math -mconstructor-aliases -munwind-tables -target-cpu generic -target-feature +neon -target-abi aapcs -mllvm -aarch64-fix-cortex-a53-835769=1 -fallow-half-arguments-and-returns -fno-split-dwarf-inlining -debug-info-kind=limited -dwarf-version=4 -debugger-tuning=gdb -v -ffunction-sections -fdata-sections -resource-dir /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9 -dependency-file CMakeFiles/cmTC_2712e.dir/CMakeCCompilerABI.c.o.d -MT CMakeFiles/cmTC_2712e.dir/CMakeCCompilerABI.c.o -sys-header-deps -D ANDROID -D _FORTIFY_SOURCE=2 -isysroot /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot -internal-isystem /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/local/include -internal-isystem /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/include -internal-externc-isystem /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include/aarch64-linux-android -internal-externc-isystem /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/include -internal-externc-isystem /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include -Wformat -fdebug-compilation-dir /media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/.cxx/RelWithDebInfo/ht1e2r36/arm64-v8a/CMakeFiles/CMakeTmp -ferror-limit 19 -stack-protector 2 -fno-signed-char -fgnuc-version=4.2.1 -o CMakeFiles/cmTC_2712e.dir/CMakeCCompilerABI.c.o -x c /opt/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c
clang -cc1 version 12.0.9 based upon LLVM 12.0.9git default target x86_64-unknown-linux-gnu
ignoring nonexistent directory "/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/include"
#include "..." search starts here:
#include <...> search starts here:
 /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/local/include
 /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/include
 /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include/aarch64-linux-android
 /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include
End of search list.
[2/2] Linking C executable cmTC_2712e
Android (8481493, based on r416183c2) clang version 12.0.9 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)
Target: aarch64-none-linux-android29
Thread model: posix
InstalledDir: /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/bin
Found candidate GCC installation: /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib/gcc/aarch64-linux-android/4.9.x
Selected GCC installation: /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib/gcc/aarch64-linux-android/4.9.x
Candidate multilib: .;@m64
Selected multilib: .;@m64
 "/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/bin/ld" --sysroot=/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot -pie -z noexecstack -EL --fix-cortex-a53-843419 --warn-shared-textrel -z now -z relro -z max-page-size=4096 --hash-style=gnu --enable-new-dtags --eh-frame-hdr -m aarch64linux -dynamic-linker /system/bin/linker64 -o cmTC_2712e /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29/crtbegin_dynamic.o -L/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/aarch64 -L/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib/gcc/aarch64-linux-android/4.9.x -L/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29 -L/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android -L/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib --build-id=sha1 --no-rosegment --fatal-warnings --no-undefined --gc-sections CMakeFiles/cmTC_2712e.dir/CMakeCCompilerABI.c.o /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/libclang_rt.builtins-aarch64-android.a -l:libunwind.a -ldl -lc /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/libclang_rt.builtins-aarch64-android.a -l:libunwind.a -ldl /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29/crtend_android.o



Parsed C implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/local/include]
    add: [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/include]
    add: [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include/aarch64-linux-android]
    add: [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include]
  end of search list found
  collapse include dir [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/local/include] ==> [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/local/include]
  collapse include dir [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/include] ==> [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/include]
  collapse include dir [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include/aarch64-linux-android] ==> [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include/aarch64-linux-android]
  collapse include dir [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include] ==> [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include]
  implicit include dirs: [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/local/include;/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/include;/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include/aarch64-linux-android;/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include]


Parsed C implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld\.lld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: /media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/.cxx/RelWithDebInfo/ht1e2r36/arm64-v8a/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):/opt/sdk/cmake/3.22.1/bin/ninja cmTC_2712e && [1/2] Building C object CMakeFiles/cmTC_2712e.dir/CMakeCCompilerABI.c.o]
  ignore line: [Android (8481493  based on r416183c2) clang version 12.0.9 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)]
  ignore line: [Target: aarch64-none-linux-android29]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/bin]
  ignore line: [Found candidate GCC installation: /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib/gcc/aarch64-linux-android/4.9.x]
  ignore line: [Selected GCC installation: /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib/gcc/aarch64-linux-android/4.9.x]
  ignore line: [Candidate multilib: .]
  ignore line: [@m64]
  ignore line: [Selected multilib: .]
  ignore line: [@m64]
  ignore line: [ (in-process)]
  ignore line: [ "/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/bin/clang" -cc1 -triple aarch64-none-linux-android29 -emit-obj -mrelax-all --mrelax-relocations -mnoexecstack -disable-free -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=non-leaf -fno-rounding-math -mconstructor-aliases -munwind-tables -target-cpu generic -target-feature +neon -target-abi aapcs -mllvm -aarch64-fix-cortex-a53-835769=1 -fallow-half-arguments-and-returns -fno-split-dwarf-inlining -debug-info-kind=limited -dwarf-version=4 -debugger-tuning=gdb -v -ffunction-sections -fdata-sections -resource-dir /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9 -dependency-file CMakeFiles/cmTC_2712e.dir/CMakeCCompilerABI.c.o.d -MT CMakeFiles/cmTC_2712e.dir/CMakeCCompilerABI.c.o -sys-header-deps -D ANDROID -D _FORTIFY_SOURCE=2 -isysroot /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot -internal-isystem /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/local/include -internal-isystem /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/include -internal-externc-isystem /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include/aarch64-linux-android -internal-externc-isystem /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/include -internal-externc-isystem /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include -Wformat -fdebug-compilation-dir /media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/.cxx/RelWithDebInfo/ht1e2r36/arm64-v8a/CMakeFiles/CMakeTmp -ferror-limit 19 -stack-protector 2 -fno-signed-char -fgnuc-version=4.2.1 -o CMakeFiles/cmTC_2712e.dir/CMakeCCompilerABI.c.o -x c /opt/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c]
  ignore line: [clang -cc1 version 12.0.9 based upon LLVM 12.0.9git default target x86_64-unknown-linux-gnu]
  ignore line: [ignoring nonexistent directory "/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/include"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/local/include]
  ignore line: [ /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/include]
  ignore line: [ /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include/aarch64-linux-android]
  ignore line: [ /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include]
  ignore line: [End of search list.]
  ignore line: [[2/2] Linking C executable cmTC_2712e]
  ignore line: [Android (8481493  based on r416183c2) clang version 12.0.9 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)]
  ignore line: [Target: aarch64-none-linux-android29]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/bin]
  ignore line: [Found candidate GCC installation: /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib/gcc/aarch64-linux-android/4.9.x]
  ignore line: [Selected GCC installation: /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib/gcc/aarch64-linux-android/4.9.x]
  ignore line: [Candidate multilib: .]
  ignore line: [@m64]
  ignore line: [Selected multilib: .]
  ignore line: [@m64]
  link line: [ "/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/bin/ld" --sysroot=/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot -pie -z noexecstack -EL --fix-cortex-a53-843419 --warn-shared-textrel -z now -z relro -z max-page-size=4096 --hash-style=gnu --enable-new-dtags --eh-frame-hdr -m aarch64linux -dynamic-linker /system/bin/linker64 -o cmTC_2712e /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29/crtbegin_dynamic.o -L/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/aarch64 -L/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib/gcc/aarch64-linux-android/4.9.x -L/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29 -L/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android -L/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib --build-id=sha1 --no-rosegment --fatal-warnings --no-undefined --gc-sections CMakeFiles/cmTC_2712e.dir/CMakeCCompilerABI.c.o /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/libclang_rt.builtins-aarch64-android.a -l:libunwind.a -ldl -lc /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/libclang_rt.builtins-aarch64-android.a -l:libunwind.a -ldl /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29/crtend_android.o]
    arg [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/bin/ld] ==> ignore
    arg [--sysroot=/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot] ==> ignore
    arg [-pie] ==> ignore
    arg [-znoexecstack] ==> ignore
    arg [-EL] ==> ignore
    arg [--fix-cortex-a53-843419] ==> ignore
    arg [--warn-shared-textrel] ==> ignore
    arg [-znow] ==> ignore
    arg [-zrelro] ==> ignore
    arg [-zmax-page-size=4096] ==> ignore
    arg [--hash-style=gnu] ==> ignore
    arg [--enable-new-dtags] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-m] ==> ignore
    arg [aarch64linux] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/system/bin/linker64] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_2712e] ==> ignore
    arg [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29/crtbegin_dynamic.o] ==> obj [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29/crtbegin_dynamic.o]
    arg [-L/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/aarch64] ==> dir [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/aarch64]
    arg [-L/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib/gcc/aarch64-linux-android/4.9.x] ==> dir [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib/gcc/aarch64-linux-android/4.9.x]
    arg [-L/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29] ==> dir [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29]
    arg [-L/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android] ==> dir [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android]
    arg [-L/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib] ==> dir [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib]
    arg [--build-id=sha1] ==> ignore
    arg [--no-rosegment] ==> ignore
    arg [--fatal-warnings] ==> ignore
    arg [--no-undefined] ==> ignore
    arg [--gc-sections] ==> ignore
    arg [CMakeFiles/cmTC_2712e.dir/CMakeCCompilerABI.c.o] ==> ignore
    arg [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/libclang_rt.builtins-aarch64-android.a] ==> lib [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/libclang_rt.builtins-aarch64-android.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [-ldl] ==> lib [dl]
    arg [-lc] ==> lib [c]
    arg [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/libclang_rt.builtins-aarch64-android.a] ==> lib [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/libclang_rt.builtins-aarch64-android.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [-ldl] ==> lib [dl]
    arg [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29/crtend_android.o] ==> obj [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29/crtend_android.o]
  remove lib [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/libclang_rt.builtins-aarch64-android.a]
  remove lib [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/libclang_rt.builtins-aarch64-android.a]
  collapse library dir [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/aarch64] ==> [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/aarch64]
  collapse library dir [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib/gcc/aarch64-linux-android/4.9.x] ==> [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib/gcc/aarch64-linux-android/4.9.x]
  collapse library dir [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29] ==> [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29]
  collapse library dir [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android] ==> [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android]
  collapse library dir [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib] ==> [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib]
  implicit libs: [-l:libunwind.a;dl;c;-l:libunwind.a;dl]
  implicit objs: [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29/crtbegin_dynamic.o;/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29/crtend_android.o]
  implicit dirs: [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/aarch64;/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib/gcc/aarch64-linux-android/4.9.x;/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29;/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android;/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib]
  implicit fwks: []


Detecting CXX compiler ABI info compiled with the following output:
Change Dir: /media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/.cxx/RelWithDebInfo/ht1e2r36/arm64-v8a/CMakeFiles/CMakeTmp

Run Build Command(s):/opt/sdk/cmake/3.22.1/bin/ninja cmTC_78b97 && [1/2] Building CXX object CMakeFiles/cmTC_78b97.dir/CMakeCXXCompilerABI.cpp.o
Android (8481493, based on r416183c2) clang version 12.0.9 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)
Target: aarch64-none-linux-android29
Thread model: posix
InstalledDir: /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/bin
Found candidate GCC installation: /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib/gcc/aarch64-linux-android/4.9.x
Selected GCC installation: /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib/gcc/aarch64-linux-android/4.9.x
Candidate multilib: .;@m64
Selected multilib: .;@m64
 (in-process)
 "/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++" -cc1 -triple aarch64-none-linux-android29 -emit-obj -mrelax-all --mrelax-relocations -mnoexecstack -disable-free -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=non-leaf -fno-rounding-math -mconstructor-aliases -munwind-tables -target-cpu generic -target-feature +neon -target-abi aapcs -mllvm -aarch64-fix-cortex-a53-835769=1 -fallow-half-arguments-and-returns -fno-split-dwarf-inlining -debug-info-kind=limited -dwarf-version=4 -debugger-tuning=gdb -v -ffunction-sections -fdata-sections -resource-dir /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9 -dependency-file CMakeFiles/cmTC_78b97.dir/CMakeCXXCompilerABI.cpp.o.d -MT CMakeFiles/cmTC_78b97.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -D ANDROID -D _FORTIFY_SOURCE=2 -isysroot /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot -internal-isystem /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include/c++/v1 -internal-isystem /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/local/include -internal-isystem /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/include -internal-externc-isystem /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include/aarch64-linux-android -internal-externc-isystem /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/include -internal-externc-isystem /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include -Wformat -std=c++11 -fdeprecated-macro -fdebug-compilation-dir /media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/.cxx/RelWithDebInfo/ht1e2r36/arm64-v8a/CMakeFiles/CMakeTmp -ferror-limit 19 -stack-protector 2 -fno-signed-char -fgnuc-version=4.2.1 -fcxx-exceptions -fexceptions -o CMakeFiles/cmTC_78b97.dir/CMakeCXXCompilerABI.cpp.o -x c++ /opt/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp
clang -cc1 version 12.0.9 based upon LLVM 12.0.9git default target x86_64-unknown-linux-gnu
ignoring nonexistent directory "/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/include"
#include "..." search starts here:
#include <...> search starts here:
 /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include/c++/v1
 /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/local/include
 /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/include
 /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include/aarch64-linux-android
 /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include
End of search list.
[2/2] Linking CXX executable cmTC_78b97
Android (8481493, based on r416183c2) clang version 12.0.9 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)
Target: aarch64-none-linux-android29
Thread model: posix
InstalledDir: /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/bin
Found candidate GCC installation: /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib/gcc/aarch64-linux-android/4.9.x
Selected GCC installation: /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib/gcc/aarch64-linux-android/4.9.x
Candidate multilib: .;@m64
Selected multilib: .;@m64
 "/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/bin/ld" --sysroot=/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot -pie -z noexecstack -EL --fix-cortex-a53-843419 --warn-shared-textrel -z now -z relro -z max-page-size=4096 --hash-style=gnu --enable-new-dtags --eh-frame-hdr -m aarch64linux -dynamic-linker /system/bin/linker64 -o cmTC_78b97 /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29/crtbegin_dynamic.o -L/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/aarch64 -L/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib/gcc/aarch64-linux-android/4.9.x -L/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29 -L/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android -L/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib --build-id=sha1 --no-rosegment --fatal-warnings --no-undefined --gc-sections CMakeFiles/cmTC_78b97.dir/CMakeCXXCompilerABI.cpp.o -lc++ -lm /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/libclang_rt.builtins-aarch64-android.a -l:libunwind.a -ldl -lc /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/libclang_rt.builtins-aarch64-android.a -l:libunwind.a -ldl /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29/crtend_android.o



Parsed CXX implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include/c++/v1]
    add: [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/local/include]
    add: [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/include]
    add: [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include/aarch64-linux-android]
    add: [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include]
  end of search list found
  collapse include dir [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include/c++/v1] ==> [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include/c++/v1]
  collapse include dir [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/local/include] ==> [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/local/include]
  collapse include dir [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/include] ==> [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/include]
  collapse include dir [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include/aarch64-linux-android] ==> [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include/aarch64-linux-android]
  collapse include dir [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include] ==> [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include]
  implicit include dirs: [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include/c++/v1;/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/local/include;/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/include;/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include/aarch64-linux-android;/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include]


Parsed CXX implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld\.lld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: /media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/.cxx/RelWithDebInfo/ht1e2r36/arm64-v8a/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):/opt/sdk/cmake/3.22.1/bin/ninja cmTC_78b97 && [1/2] Building CXX object CMakeFiles/cmTC_78b97.dir/CMakeCXXCompilerABI.cpp.o]
  ignore line: [Android (8481493  based on r416183c2) clang version 12.0.9 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)]
  ignore line: [Target: aarch64-none-linux-android29]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/bin]
  ignore line: [Found candidate GCC installation: /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib/gcc/aarch64-linux-android/4.9.x]
  ignore line: [Selected GCC installation: /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib/gcc/aarch64-linux-android/4.9.x]
  ignore line: [Candidate multilib: .]
  ignore line: [@m64]
  ignore line: [Selected multilib: .]
  ignore line: [@m64]
  ignore line: [ (in-process)]
  ignore line: [ "/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++" -cc1 -triple aarch64-none-linux-android29 -emit-obj -mrelax-all --mrelax-relocations -mnoexecstack -disable-free -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=non-leaf -fno-rounding-math -mconstructor-aliases -munwind-tables -target-cpu generic -target-feature +neon -target-abi aapcs -mllvm -aarch64-fix-cortex-a53-835769=1 -fallow-half-arguments-and-returns -fno-split-dwarf-inlining -debug-info-kind=limited -dwarf-version=4 -debugger-tuning=gdb -v -ffunction-sections -fdata-sections -resource-dir /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9 -dependency-file CMakeFiles/cmTC_78b97.dir/CMakeCXXCompilerABI.cpp.o.d -MT CMakeFiles/cmTC_78b97.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -D ANDROID -D _FORTIFY_SOURCE=2 -isysroot /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot -internal-isystem /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include/c++/v1 -internal-isystem /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/local/include -internal-isystem /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/include -internal-externc-isystem /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include/aarch64-linux-android -internal-externc-isystem /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/include -internal-externc-isystem /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include -Wformat -std=c++11 -fdeprecated-macro -fdebug-compilation-dir /media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/.cxx/RelWithDebInfo/ht1e2r36/arm64-v8a/CMakeFiles/CMakeTmp -ferror-limit 19 -stack-protector 2 -fno-signed-char -fgnuc-version=4.2.1 -fcxx-exceptions -fexceptions -o CMakeFiles/cmTC_78b97.dir/CMakeCXXCompilerABI.cpp.o -x c++ /opt/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp]
  ignore line: [clang -cc1 version 12.0.9 based upon LLVM 12.0.9git default target x86_64-unknown-linux-gnu]
  ignore line: [ignoring nonexistent directory "/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/include"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include/c++/v1]
  ignore line: [ /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/local/include]
  ignore line: [ /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/include]
  ignore line: [ /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include/aarch64-linux-android]
  ignore line: [ /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/include]
  ignore line: [End of search list.]
  ignore line: [[2/2] Linking CXX executable cmTC_78b97]
  ignore line: [Android (8481493  based on r416183c2) clang version 12.0.9 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)]
  ignore line: [Target: aarch64-none-linux-android29]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/bin]
  ignore line: [Found candidate GCC installation: /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib/gcc/aarch64-linux-android/4.9.x]
  ignore line: [Selected GCC installation: /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib/gcc/aarch64-linux-android/4.9.x]
  ignore line: [Candidate multilib: .]
  ignore line: [@m64]
  ignore line: [Selected multilib: .]
  ignore line: [@m64]
  link line: [ "/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/bin/ld" --sysroot=/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot -pie -z noexecstack -EL --fix-cortex-a53-843419 --warn-shared-textrel -z now -z relro -z max-page-size=4096 --hash-style=gnu --enable-new-dtags --eh-frame-hdr -m aarch64linux -dynamic-linker /system/bin/linker64 -o cmTC_78b97 /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29/crtbegin_dynamic.o -L/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/aarch64 -L/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib/gcc/aarch64-linux-android/4.9.x -L/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29 -L/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android -L/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib --build-id=sha1 --no-rosegment --fatal-warnings --no-undefined --gc-sections CMakeFiles/cmTC_78b97.dir/CMakeCXXCompilerABI.cpp.o -lc++ -lm /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/libclang_rt.builtins-aarch64-android.a -l:libunwind.a -ldl -lc /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/libclang_rt.builtins-aarch64-android.a -l:libunwind.a -ldl /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29/crtend_android.o]
    arg [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/bin/ld] ==> ignore
    arg [--sysroot=/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot] ==> ignore
    arg [-pie] ==> ignore
    arg [-znoexecstack] ==> ignore
    arg [-EL] ==> ignore
    arg [--fix-cortex-a53-843419] ==> ignore
    arg [--warn-shared-textrel] ==> ignore
    arg [-znow] ==> ignore
    arg [-zrelro] ==> ignore
    arg [-zmax-page-size=4096] ==> ignore
    arg [--hash-style=gnu] ==> ignore
    arg [--enable-new-dtags] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-m] ==> ignore
    arg [aarch64linux] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/system/bin/linker64] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_78b97] ==> ignore
    arg [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29/crtbegin_dynamic.o] ==> obj [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29/crtbegin_dynamic.o]
    arg [-L/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/aarch64] ==> dir [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/aarch64]
    arg [-L/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib/gcc/aarch64-linux-android/4.9.x] ==> dir [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib/gcc/aarch64-linux-android/4.9.x]
    arg [-L/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29] ==> dir [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29]
    arg [-L/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android] ==> dir [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android]
    arg [-L/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib] ==> dir [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib]
    arg [--build-id=sha1] ==> ignore
    arg [--no-rosegment] ==> ignore
    arg [--fatal-warnings] ==> ignore
    arg [--no-undefined] ==> ignore
    arg [--gc-sections] ==> ignore
    arg [CMakeFiles/cmTC_78b97.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
    arg [-lc++] ==> lib [c++]
    arg [-lm] ==> lib [m]
    arg [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/libclang_rt.builtins-aarch64-android.a] ==> lib [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/libclang_rt.builtins-aarch64-android.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [-ldl] ==> lib [dl]
    arg [-lc] ==> lib [c]
    arg [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/libclang_rt.builtins-aarch64-android.a] ==> lib [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/libclang_rt.builtins-aarch64-android.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [-ldl] ==> lib [dl]
    arg [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29/crtend_android.o] ==> obj [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29/crtend_android.o]
  remove lib [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/libclang_rt.builtins-aarch64-android.a]
  remove lib [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/libclang_rt.builtins-aarch64-android.a]
  collapse library dir [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/aarch64] ==> [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/aarch64]
  collapse library dir [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib/gcc/aarch64-linux-android/4.9.x] ==> [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib/gcc/aarch64-linux-android/4.9.x]
  collapse library dir [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29] ==> [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29]
  collapse library dir [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android] ==> [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android]
  collapse library dir [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib] ==> [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib]
  implicit libs: [c++;m;-l:libunwind.a;dl;c;-l:libunwind.a;dl]
  implicit objs: [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29/crtbegin_dynamic.o;/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29/crtend_android.o]
  implicit dirs: [/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib64/clang/12.0.9/lib/linux/aarch64;/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/lib/gcc/aarch64-linux-android/4.9.x;/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/29;/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android;/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib]
  implicit fwks: []


