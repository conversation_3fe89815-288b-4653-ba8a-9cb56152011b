<?xml version="1.0" encoding="utf-8"?>
<!--
 * Copyright (C) 2023-2024 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 * All Rights Reserved.
 * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *
 * Not a contribution.
-->
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:background="@android:drawable/dialog_holo_light_frame">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <Button
                android:id="@+id/btn_select_file"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Select File"
                android:drawableLeft="@android:drawable/ic_menu_upload"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="8dp">

                <Button
                    android:id="@+id/btn_encrypt"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginRight="4dp"
                    android:text="Encrypt"
                    android:drawableLeft="@android:drawable/ic_lock_lock"/>

                <Button
                    android:id="@+id/btn_decrypt"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginLeft="4dp"
                    android:text="Decrypt"
                    android:drawableLeft="@android:drawable/ic_lock_idle_lock"/>
            </LinearLayout>
        </LinearLayout>
    </FrameLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@android:drawable/dialog_holo_light_frame">

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/tv_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="16dp"
                android:textSize="14sp"/>
        </ScrollView>
    </FrameLayout>
</LinearLayout>