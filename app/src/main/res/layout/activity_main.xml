<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="8dp">

        <Button
            android:id="@+id/test_stream_encrypt_btn"
            android:layout_width="match_parent"
            android:layout_height="32dp"
            android:textSize="12sp"
        android:text="@string/test_stream_encrypt" />

        <Button
        android:id="@+id/test_stream_decrypt_btn"
            android:layout_width="match_parent"
            android:layout_height="32dp"
            android:textSize="12sp"
            android:layout_marginTop="4dp"
        android:text="@string/test_stream_decrypt" />

    <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
        android:orientation="horizontal"
            android:layout_marginTop="4dp">

        <Button
            android:id="@+id/test_video_stream_encrypt_btn"
            android:layout_width="0dp"
                android:layout_height="32dp"
                android:textSize="10sp"
            android:layout_weight="1"
                android:layout_marginRight="2dp"
            android:text="@string/test_video_stream_encrypt" />

        <Button
            android:id="@+id/test_video_stream_decrypt_btn"
            android:layout_width="0dp"
                android:layout_height="32dp"
                android:textSize="10sp"
            android:layout_weight="1"
                android:layout_marginLeft="2dp"
            android:text="@string/test_video_stream_decrypt" />

    </LinearLayout>

    <ProgressBar
        android:id="@+id/progress_bar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
        android:visibility="invisible" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textSize="12sp"
        android:text="@string/status_title"
        android:textStyle="bold" />

        <!-- 状态输出框，固定高度 -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:layout_marginTop="4dp"
            android:background="@android:drawable/edit_text">

    <ScrollView
        android:layout_width="match_parent"
                android:layout_height="match_parent">

        <TextView
            android:id="@+id/status_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
                    android:padding="4dp"
                    android:textSize="10sp"
            android:typeface="monospace" />

    </ScrollView>
        </FrameLayout>

</LinearLayout> 
</ScrollView> 