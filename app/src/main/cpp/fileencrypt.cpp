/*
* Copyright (C) 2025 THUNDERCOMM TECHNOLOGY Co.,Ltd.
* All Rights Reserved.
* Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
*/

#include <jni.h>
#include "fileencrypt.h"
#include <openssl/evp.h>
#include <openssl/aes.h>
#include <openssl/err.h>
#include <android/log.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <unistd.h>
#include <fstream>
#include <vector>
#include <cstdint>
#include <algorithm>
#include <cerrno>
#include <cstdio>
#include <cstring>
#include <stdexcept>

#define LOG_TAG "FileEncrypt_Native"
#ifndef ANDROID_LOG_DEBUG
#define ANDROID_LOG_DEBUG 3
#endif
#ifndef ANDROID_LOG_INFO
#define ANDROID_LOG_INFO 4
#endif
#ifndef ANDROID_LOG_ERROR
#define ANDROID_LOG_ERROR 6
#endif

#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__)

using namespace fileencrypt;
using namespace std;

namespace {
    class ScopedEvpCipherCtx {
    public:
        ScopedEvpCipherCtx() : ctx(EVP_CIPHER_CTX_new()) {}
        ~ScopedEvpCipherCtx() {
            if (ctx) {
                EVP_CIPHER_CTX_free(ctx);
            }
        }
        EVP_CIPHER_CTX* get() { return ctx; }
    private:
        EVP_CIPHER_CTX* ctx;
    };
}

JNIEXPORT jboolean JNICALL
Java_com_thundercomm_crown_fileencrypt_utils_NativeCrypto_processStream(
        JNIEnv* env,
        jobject /* this */,
        jint inputFd,
        jint outputFd,
        jbyteArray key,
        jbyteArray iv,
        jboolean isEncrypt) {
    LOGD("Processing stream: isEncrypt=%d, inputFd=%d, outputFd=%d",
         isEncrypt, inputFd, outputFd);

    if (inputFd < 0 || outputFd < 0) {
        LOGE("Invalid file descriptors: inputFd=%d, outputFd=%d", inputFd, outputFd);
        return JNI_FALSE;
    }

    jbyte* keyData = env->GetByteArrayElements(key, nullptr);
    jbyte* ivData = env->GetByteArrayElements(iv, nullptr);

    if (keyData == nullptr || ivData == nullptr) {
        LOGE("Failed to get key or IV data");
        if (keyData) env->ReleaseByteArrayElements(key, keyData, JNI_ABORT);
        return JNI_FALSE;
    }

    jsize keyLength = env->GetArrayLength(key);
    jsize ivLength = env->GetArrayLength(iv);

    if (keyLength != AES_KEY_SIZE_BYTES || ivLength != GCM_IV_LENGTH) {
        LOGE("Invalid key or IV length: keyLength=%d, ivLength=%d", keyLength, ivLength);
        env->ReleaseByteArrayElements(key, keyData, JNI_ABORT);
        env->ReleaseByteArrayElements(iv, ivData, JNI_ABORT);
        return JNI_FALSE;
    }

    int inFdCopy = dup(inputFd);
    int outFdCopy = dup(outputFd);

    if (inFdCopy < 0 || outFdCopy < 0) {
        LOGE("Failed to duplicate file descriptors: errno=%d (%s)", errno, strerror(errno));
        if (inFdCopy >= 0) close(inFdCopy);
        if (outFdCopy >= 0) close(outFdCopy);
        env->ReleaseByteArrayElements(key, keyData, JNI_ABORT);
        env->ReleaseByteArrayElements(iv, ivData, JNI_ABORT);
        return JNI_FALSE;
    }

    FILE* inFile = fdopen(inFdCopy, "rb");
    FILE* outFile = fdopen(outFdCopy, "wb");

    if (!inFile || !outFile) {
        LOGE("Failed to open file descriptors: errno=%d (%s)", errno, strerror(errno));
        if (inFile) fclose(inFile); else close(inFdCopy);
        if (outFile) fclose(outFile); else close(outFdCopy);
        env->ReleaseByteArrayElements(key, keyData, JNI_ABORT);
        env->ReleaseByteArrayElements(iv, ivData, JNI_ABORT);
        return JNI_FALSE;
    }

    ScopedEvpCipherCtx scopedCtx;
    EVP_CIPHER_CTX* ctx = scopedCtx.get();
    bool success = false;

    try {
        if (isEncrypt == JNI_TRUE) {
            if (!EVP_EncryptInit_ex(ctx, EVP_aes_256_gcm(), nullptr,
                                    reinterpret_cast<const uint8_t*>(keyData),
                                    reinterpret_cast<const uint8_t*>(ivData))) {
                throw std::runtime_error("Failed to initialize encryption context");
            }

            if (fwrite(ivData, 1, GCM_IV_LENGTH, outFile) != GCM_IV_LENGTH) {
                throw std::runtime_error("Failed to write IV");
            }

            std::vector<uint8_t> buffer(CHUNK_SIZE);
            std::vector<uint8_t> outBuffer(CHUNK_SIZE + EVP_MAX_BLOCK_LENGTH);
            int outLen;

            size_t bytesRead;
            while ((bytesRead = fread(buffer.data(), 1, CHUNK_SIZE, inFile)) > 0) {
                if (!EVP_EncryptUpdate(ctx, outBuffer.data(), &outLen, buffer.data(), bytesRead)) {
                    throw std::runtime_error("Failed to process data chunk");
                }
                if (outLen > 0 && fwrite(outBuffer.data(), 1, outLen, outFile) != (size_t)outLen) {
                    throw std::runtime_error("Failed to write to output stream");
                }
            }
            if (ferror(inFile)) throw std::runtime_error("Error reading from input stream");

            if (!EVP_EncryptFinal_ex(ctx, outBuffer.data(), &outLen)) {
                throw std::runtime_error("Failed to finalize encryption");
            }
            if (outLen > 0 && fwrite(outBuffer.data(), 1, outLen, outFile) != (size_t)outLen) {
                throw std::runtime_error("Failed to write final block");
            }

            unsigned char tag[GCM_TAG_LENGTH];
            if (!EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_GCM_GET_TAG, GCM_TAG_LENGTH, tag)) {
                throw std::runtime_error("Failed to get authentication tag");
            }
            if (fwrite(tag, 1, GCM_TAG_LENGTH, outFile) != GCM_TAG_LENGTH) {
                throw std::runtime_error("Failed to write authentication tag");
            }
        } else { // Decrypt
            unsigned char fileIv[GCM_IV_LENGTH];
            if (fread(fileIv, 1, GCM_IV_LENGTH, inFile) != GCM_IV_LENGTH) {
                throw std::runtime_error("Failed to read IV");
            }

            if (!EVP_DecryptInit_ex(ctx, EVP_aes_256_gcm(), nullptr,
                                    reinterpret_cast<const uint8_t*>(keyData), fileIv)) {
                throw std::runtime_error("Failed to initialize decryption context");
            }

            fseek(inFile, 0, SEEK_END);
            long fileSize = ftell(inFile);
            if (fileSize < GCM_IV_LENGTH + GCM_TAG_LENGTH) {
                throw std::runtime_error("Input file too small");
            }
            size_t encryptedDataSize = fileSize - GCM_IV_LENGTH - GCM_TAG_LENGTH;

            unsigned char tag[GCM_TAG_LENGTH];
            fseek(inFile, fileSize - GCM_TAG_LENGTH, SEEK_SET);
            if (fread(tag, 1, GCM_TAG_LENGTH, inFile) != GCM_TAG_LENGTH) {
                throw std::runtime_error("Failed to read authentication tag");
            }

            if (!EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_GCM_SET_TAG, GCM_TAG_LENGTH, tag)) {
                throw std::runtime_error("Failed to set authentication tag");
            }

            fseek(inFile, GCM_IV_LENGTH, SEEK_SET);

            std::vector<uint8_t> inBuffer(BUFFER_SIZE);
            std::vector<uint8_t> outBuffer(BUFFER_SIZE + EVP_MAX_BLOCK_LENGTH);
            size_t totalBytesRead = 0;
            int outLen;

            while (totalBytesRead < encryptedDataSize) {
                size_t bytesToRead = std::min((size_t)BUFFER_SIZE, encryptedDataSize - totalBytesRead);
                size_t bytesRead = fread(inBuffer.data(), 1, bytesToRead, inFile);
                if (bytesRead != bytesToRead) {
                    throw std::runtime_error("Failed to read encrypted data");
                }

                if (!EVP_DecryptUpdate(ctx, outBuffer.data(), &outLen, inBuffer.data(), bytesRead)) {
                    throw std::runtime_error("Failed to decrypt data chunk");
                }
                if (outLen > 0 && fwrite(outBuffer.data(), 1, outLen, outFile) != (size_t)outLen) {
                    throw std::runtime_error("Failed to write decrypted data");
                }
                totalBytesRead += bytesRead;
            }

            if (!EVP_DecryptFinal_ex(ctx, outBuffer.data(), &outLen)) {
                throw std::runtime_error("Failed to finalize decryption");
            }
            if (outLen > 0 && fwrite(outBuffer.data(), 1, outLen, outFile) != (size_t)outLen) {
                throw std::runtime_error("Failed to write final block");
            }
        }
        if (fflush(outFile) != 0) {
            throw std::runtime_error("Failed to flush output stream");
        }
        success = true;
    } catch (const std::exception& e) {
        LOGE("Exception during stream processing: %s", e.what());
        const unsigned long err = ERR_get_error();
        if (err != 0) {
            char err_buf[256];
            ERR_error_string_n(err, err_buf, sizeof(err_buf));
            LOGE("OpenSSL error: %s", err_buf);
        }
        success = false;
    }

    if (inFile) fclose(inFile);
    if (outFile) fclose(outFile);
    env->ReleaseByteArrayElements(key, keyData, JNI_ABORT);
    env->ReleaseByteArrayElements(iv, ivData, JNI_ABORT);

    return success ? JNI_TRUE : JNI_FALSE;
}