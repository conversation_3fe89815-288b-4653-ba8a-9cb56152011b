/*
* Copyright (C) 2025 THUNDERCOMM TECHNOLOGY Co.,Ltd.
* All Rights Reserved.
* Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
*/

#ifndef FILEENCRYPT_H
#define FILEENCRYPT_H

#include <jni.h>
#include <string>
#include <vector>
#include <fstream>
#include <android/log.h>
#include <openssl/evp.h>
#include <openssl/aes.h>
#include <openssl/err.h>

#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

/**
 * File encryption related constants and interfaces
 */
namespace fileencrypt {
    // Constants should match Constants.java
    static constexpr size_t GCM_IV_LENGTH = 12;       // Must match Constants.GCM_IV_LENGTH
    static constexpr size_t GCM_TAG_LENGTH = 16;      // 128 bits = 16 bytes
    static constexpr size_t AES_KEY_SIZE_BYTES = 32;  // 256 bits = 32 bytes
    static constexpr size_t BUFFER_SIZE = 8192;       // Matches Constants.LARGE_BUFFER_SIZE
    static constexpr size_t CHUNK_SIZE = 1024 * 1024; // 1MB for stream processing
}

extern "C" {
    JNIEXPORT jboolean JNICALL
    Java_com_thundercomm_crown_fileencrypt_utils_NativeCrypto_processStream(
            JNIEnv* env,
            jobject /* this */,
            jint inputFd,
            jint outputFd,
            jbyteArray key,
            jbyteArray iv,
            jboolean isEncrypt);
}

#endif // FILEENCRYPT_H