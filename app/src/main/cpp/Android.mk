# Copyright (C) 2009 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
LOCAL_PATH := $(call my-dir)

# ==============================================================================
# Build libfileencrypt, linking against system OpenSSL/BoringSSL
# ==============================================================================
include $(CLEAR_VARS)

LOCAL_MODULE := libfileencrypt
LOCAL_MODULE_TAGS := optional

LOCAL_SRC_FILES := \
    fileencrypt.cpp

LOCAL_C_INCLUDES := \
    system/security/keystore/include \
    external/boringssl/include \
    external/openssl/include

LOCAL_HEADER_LIBRARIES := \
    jni_headers \
    libnativehelper_header_only \
    libcutils_headers \
    libhardware_headers \
    libcrypto_headers \
    libssl_headers

LOCAL_SHARED_LIBRARIES := \
    liblog \
    libutils \
    libcutils \
    libcrypto \
    libssl

LOCAL_CFLAGS := \
    -Wall \
    -Wextra \
    -Wno-unused-parameter \
    -fPIC

LOCAL_CPPFLAGS := \
    -std=c++17 \
    -fexceptions \
    -frtti

include $(BUILD_SHARED_LIBRARY)