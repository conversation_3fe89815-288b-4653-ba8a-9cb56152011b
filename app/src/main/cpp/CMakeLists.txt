cmake_minimum_required(VERSION 3.18.1)

project(fileencrypt)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set BoringSSL path
set(BORINGSSL_ROOT ${CMAKE_CURRENT_SOURCE_DIR}/../openssl/boringssl)

# Set NDK path
if(ANDROID_NDK)
    set(NDK_ROOT ${ANDROID_NDK})
else()
    if(DEFINED ENV{ANDROID_NDK_HOME})
        set(NDK_ROOT $ENV{ANDROID_NDK_HOME})
    else()
        message(FATAL_ERROR "Neither ANDROID_NDK nor ANDROID_NDK_HOME is set")
    endif()
endif()

# Disable BoringSSL tests and examples
set(BUILD_TESTING OFF CACHE BOOL "Disable BoringSSL tests")
set(BUILD_SHARED_LIBS OFF CACHE BOOL "Build static libs")
set(C<PERSON>KE_POSITION_INDEPENDENT_CODE ON)

# Add BoringSSL
add_subdirectory(${BORINGSSL_ROOT} boringssl)

# Include header directories
include_directories(
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${BORINGSSL_ROOT}/include
        ${NDK_ROOT}/sysroot/usr/include
        ${NDK_ROOT}/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include
)

# Add source files
add_library(fileencrypt SHARED
        fileencrypt.cpp
)

# Link BoringSSL, use static linking to avoid runtime dependencies
target_link_libraries(fileencrypt
        crypto # BoringSSL crypto library
        ssl    # BoringSSL ssl library
        log
        android
)