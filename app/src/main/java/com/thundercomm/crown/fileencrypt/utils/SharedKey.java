/*
* Copyright (C) 2025 THUNDERCOMM TECHNOLOGY Co.,Ltd.
* All Rights Reserved.
* Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
*/
package com.thundercomm.crown.fileencrypt.utils;
import java.security.spec.KeySpec;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import com.thundercomm.crown.fileencrypt.Constants;
/**
 * Utility class for generating shared keys based on device ID.
 */
public class SharedKey {
    private static final String TAG = "SharedKey";
    // "Pepper" shared with the server, in production environment it should come from secure configuration
    private static final byte[] PEPPER = new byte[]{
        (byte)0xAB,(byte)0xCD,(byte)0x12,(byte)0x34,
        (byte)0x56,(byte)0x78,(byte)0x9A,(byte)0xBC,
        (byte)0xDE,(byte)0xF0,(byte)0x11,(byte)0x22,
        (byte)0x33,(byte)0x44,(byte)0x55,(byte)0x66
    };
    /**
     * Derive AES key based on device ID.
     *
     * @param deviceId Unique device identifier
     * @return Derived AES key
     * @throws Exception If key derivation fails
     */
    public static SecretKey deriveKey(String deviceId) throws Exception {
        if (deviceId == null || deviceId.isEmpty()) {
            throw new IllegalArgumentException("Device ID cannot be empty");
        }
        Logger.d(TAG, "Starting key derivation based on device ID");
        Logger.d(TAG, "Using PBKDF2WithHmacSHA256 algorithm");
        Logger.d(TAG, "Iterations: " + Constants.PBKDF2_ITERATIONS);
        Logger.d(TAG, "Target key length: " + Constants.AES_KEY_SIZE + " bits");
        // Log hexadecimal representation of pepper (not the actual value)
        StringBuilder pepperHex = new StringBuilder();
        for (byte b : PEPPER) {
            pepperHex.append(String.format("%02X", b));
        }
        Logger.d(TAG, "Using 16-byte Pepper: " + pepperHex.substring(0, 8) + "..." +
                pepperHex.substring(pepperHex.length() - 8));
        long startTime = System.currentTimeMillis();
        // Pass device ID as "password" to PBKDF2
        KeySpec spec = new PBEKeySpec(
            deviceId.toCharArray(),
            PEPPER,
            Constants.PBKDF2_ITERATIONS,
            Constants.AES_KEY_SIZE
        );
        try {
            Logger.d(TAG, "Creating SecretKeyFactory...");
            SecretKeyFactory factory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256");
            Logger.d(TAG, "Generating key...");
            byte[] keyBytes = factory.generateSecret(spec).getEncoded();
            // Create AES key
            SecretKey key = new SecretKeySpec(keyBytes, "AES");
            long endTime = System.currentTimeMillis();
            Logger.d(TAG, "Key derivation successful, time cost: " + (endTime - startTime) + " ms");
            Logger.d(TAG, "Key length: " + (keyBytes.length * 8) + " bits");
            // Log hexadecimal representation of the first few bytes of the key (not the actual key)
            StringBuilder keyHex = new StringBuilder();
            for (int i = 0; i < Math.min(keyBytes.length, 8); i++) {
                keyHex.append(String.format("%02X", keyBytes[i]));
            }
            Logger.d(TAG, "Key prefix: " + keyHex + "...");
            return key;
        } catch (Exception exception) {
            Logger.e(TAG, "Key derivation failed", exception);
            throw exception;
        }
    }
}