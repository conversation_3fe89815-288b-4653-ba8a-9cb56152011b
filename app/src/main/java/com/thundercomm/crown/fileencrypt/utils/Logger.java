/*
* Copyright (C) 2025 THUNDERCOMM TECHNOLOGY Co.,Ltd.
* All Rights Reserved.
* Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
*/
package com.thundercomm.crown.fileencrypt.utils;
import android.util.Log;
/**
 * Utility class for logging with configurable levels and UI callback support.
 */
public class Logger {
    private static final String TAG = "FileEncrypt";
    // Log level control
    private static final boolean DEBUG = Log.isLoggable(TAG, Log.DEBUG);

    private Logger() {
        // Private constructor to prevent instantiation
    }

    /**
     * Sets debug mode (deprecated).
     *
     * @param debug Whether debug logging is enabled
     */
    public static void setDebug(boolean debug) {
        // This method is no longer used as DEBUG is now controlled by system properties
    }

    /**
     * Checks if debug logging is enabled.
     *
     * @return true if debug logging is enabled
     */
    public static boolean isDebug() {
        return DEBUG;
    }

    /**
     * Logs a debug message.
     *
     * @param tag Message tag
     * @param msg Message content
     */
    public static void d(String tag, String msg) {
        if (DEBUG) {
            Log.d(TAG + "_" + tag, msg);
        }
    }

    /**
     * Logs an info message.
     *
     * @param tag Message tag
     * @param msg Message content
     */
    public static void i(String tag, String msg) {
        if (DEBUG) {
            Log.i(TAG + "_" + tag, msg);
        }
    }

    /**
     * Logs a warning message.
     *
     * @param tag Message tag
     * @param msg Message content
     */
    public static void w(String tag, String msg) {
        if (DEBUG) {
            Log.w(TAG + "_" + tag, msg);
        }
    }

    /**
     * Logs an error message.
     *
     * @param tag Message tag
     * @param msg Message content
     */
    public static void e(String tag, String msg) {
        if (DEBUG) {
            Log.e(TAG + "_" + tag, msg);
        }
    }

    /**
     * Logs an error message with exception.
     *
     * @param tag Message tag
     * @param msg Message content
     * @param tr Throwable exception
     */
    public static void e(String tag, String msg, Throwable tr) {
        if (DEBUG) {
            Log.e(TAG + "_" + tag, msg, tr);
        }
    }

    /**
     * Logs a verbose message.
     *
     * @param tag Message tag
     * @param message Message content
     */
    public static void v(String tag, String message) {
        if (DEBUG) {
            Log.v(TAG + "_" + tag, message);
        }
    }

    /**
     * Dumps byte array data in hexadecimal format.
     *
     * @param tag Message tag
     * @param prefix Prefix to add before hex data
     * @param data Byte array to dump
     */
    public static void hexDump(String tag, String prefix, byte[] data) {
        if (!DEBUG) {
            return;
        }
        if (data == null) {
            d(tag, prefix + ": null");
            return;
        }
        StringBuilder hexString = new StringBuilder();
        for (int i = 0; i < data.length; i++) {
            hexString.append(String.format("%02x", data[i]));
            if ((i + 1) % 16 == 0) hexString.append("\n");
            else hexString.append(" ");
        }
        d(tag, prefix + ":\n" + hexString.toString());
    }

    /**
     * Callback interface for UI display of log messages.
     */
    public interface LogCallback {
        /**
         * Called when a log message is generated.
         * 
         * @param message The log message
         */
        void onLog(String message);
    }

    private static LogCallback logCallback;

    /**
     * Sets callback for log messages.
     *
     * @param callback Callback to receive log messages
     */
    public static void setLogCallback(LogCallback callback) {
        logCallback = callback;
    }

    /**
     * Logs a message and sends it to UI callback if registered.
     *
     * @param message The message to log
     */
    public static void log(String message) {
        if (DEBUG) {
            d(TAG, message);
            if (logCallback != null) {
                logCallback.onLog(message);
            }
        }
    }
}