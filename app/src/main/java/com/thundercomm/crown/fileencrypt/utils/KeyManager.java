/*
* Copyright (C) 2025 THUNDERCOMM TECHNOLOGY Co.,Ltd.
* All Rights Reserved.
* Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
*/
package com.thundercomm.crown.fileencrypt.utils;
import android.content.Context;
import android.content.SharedPreferences;
import android.util.Base64;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import com.thundercomm.crown.fileencrypt.Constants;
/**
 * Manages encryption key creation, storage and retrieval.
 */
public class KeyManager {
    private static final String TAG = "KeyManager";
    private final Context mContext;
    private final File mStorageDir;
    private SecretKey mAESKey = null;
    private ByteBuffer mAESKeyBuffer = null;
    /**
     * Creates a KeyManager instance.
     *
     * @param context Application context
     * @param storageDir Key storage directory
     */
    public KeyManager(Context context, File storageDir) {
        mContext = context;
        mStorageDir = storageDir;
        Logger.i(TAG, "KeyManager initialized, storage directory: " + storageDir.getAbsolutePath());
    }

    /**
     * Get device protected storage directory for key files.
     * This directory is accessible before user unlock.
     */
    private File getDeviceProtectedStorageDir() {
        Context deviceContext = mContext.createDeviceProtectedStorageContext();
        return deviceContext.getFilesDir();
    }
    /**
     * Initializes key manager, creates or loads keys.
     *
     * @throws Exception Thrown when initialization fails
     */
    public void initialize() throws Exception {
        Logger.i(TAG, "Starting key manager initialization");
        // Generate or load KEK(Key Encryption Key)
//        createOrLoadKEK();
        // Check if AES key has been generated before
        // Use device protected storage context to access SharedPreferences before user unlock
        Context deviceContext = mContext.createDeviceProtectedStorageContext();
        SharedPreferences prefs = deviceContext.getSharedPreferences(Constants.PREFS_NAME, Context.MODE_PRIVATE);
        boolean keyGenerated = prefs.getBoolean(Constants.PREF_KEY_AES_KEY_GENERATED, false);
        Logger.d(TAG, "Checking key status: keyGenerated = " + keyGenerated);
        // Try to load existing wrapped CEK from device protected storage
        File wrappedCekFile = new File(getDeviceProtectedStorageDir(), Constants.WRAPPED_CEK_FILE);
        Logger.d(TAG, "Key file path: " + wrappedCekFile.getAbsolutePath());
        Logger.d(TAG, "Key file exists: " + wrappedCekFile.exists());
        if (keyGenerated && wrappedCekFile.exists()) {
            try {
                Logger.d(TAG, "Attempting to load existing key...");
                loadWrappedCEK();
                Logger.i(TAG, "Successfully loaded existing AES key");
                return;
            } catch (Exception exception) {
                Logger.w(TAG, "Failed to load existing wrapped CEK, will regenerate: " + exception.getMessage());
                if (!wrappedCekFile.delete()) {
                    Logger.e(TAG, "Cannot delete corrupted wrapped CEK file");
                } else {
                    Logger.d(TAG, "Deleted corrupted key file");
                }
            }
        }
        // If no key or loading failed, generate new key based on device ID
        Logger.i(TAG, "Starting to generate new key based on device ID");
        generateAESKeyFromDeviceId();
    }
    /**
     * Generates AES key based on device ID.
     * 
     * @throws Exception Thrown when generation fails
     */
    private void generateAESKeyFromDeviceId() throws Exception {
        Logger.d(TAG, "Starting to generate AES key based on device ID");
        // Get device ID
        String deviceId = DeviceUtils.getDeviceId(mContext);
        Logger.d(TAG, "Device ID obtained");
        // Derive AES key based on device ID
        Logger.d(TAG, "Starting AES key derivation...");
        SecretKey aesKey = SharedKey.deriveKey(deviceId);
        Logger.d(TAG, "AES key derivation completed");
        // Get key bytes
        byte[] aesKeyBytes = aesKey.getEncoded();
        Logger.d(TAG, "AES key bytes obtained, length: " + aesKeyBytes.length);
        // Save key directly to file
        Logger.d(TAG, "Preparing to save key to file...");
        saveKeyToFile(aesKeyBytes);
        // Set key in memory
        mAESKey = aesKey;
        Logger.d(TAG, "Key object set in memory");
        // Put CEK into DirectByteBuffer for native layer use
        if (mAESKeyBuffer != null) {
            mAESKeyBuffer.clear();
            Logger.d(TAG, "Cleared old key buffer");
        }
        mAESKeyBuffer = ByteBuffer.allocateDirect(aesKeyBytes.length);
        mAESKeyBuffer.put(aesKeyBytes);
        mAESKeyBuffer.flip();
        Logger.d(TAG, "Created DirectByteBuffer for native layer use");
        // Clear key plaintext from temporary variables
        java.util.Arrays.fill(aesKeyBytes, (byte) 0);
        Logger.d(TAG, "Cleared key plaintext from temporary variables");
        // Mark key as generated
        // Use device protected storage context to save the flag
        Context deviceContext = mContext.createDeviceProtectedStorageContext();
        SharedPreferences prefs = deviceContext.getSharedPreferences(Constants.PREFS_NAME, Context.MODE_PRIVATE);
        prefs.edit().putBoolean(Constants.PREF_KEY_AES_KEY_GENERATED, true).apply();
        Logger.d(TAG, "Key generation status marked as true");
        Logger.i(TAG, "Successfully generated and saved AES key based on device ID");
    }
    /**
     * Saves key to file.
     * 
     * @param keyBytes Key byte array
     * @throws Exception Thrown when saving fails
     */
    private void saveKeyToFile(byte[] keyBytes) throws Exception {
        Logger.d(TAG, "Starting to save key to file");
        File deviceStorageDir = getDeviceProtectedStorageDir();
        File wrappedCekFile = new File(deviceStorageDir, Constants.WRAPPED_CEK_FILE);
        File tempFile = new File(deviceStorageDir, Constants.WRAPPED_CEK_FILE + ".tmp");
        // First write to temporary file
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            Logger.d(TAG, "Writing key to temporary file: " + tempFile.getAbsolutePath());
            fos.write(keyBytes);
            fos.getFD().sync(); // Ensure written to disk
            Logger.d(TAG, "Key writing to temporary file completed");
        }
        // Atomically rename temporary file
        if (!tempFile.renameTo(wrappedCekFile)) {
            throw new IOException("Unable to rename temporary file to " + Constants.WRAPPED_CEK_FILE);
        }
        Logger.d(TAG, "Temporary file successfully renamed to official key file");
    }
    /**
     * Loads encryption key from file.
     * 
     * @throws Exception Thrown when loading fails
     */
    private void loadWrappedCEK() throws Exception {
        Logger.d(TAG, "Starting to load key from file");
        File wrappedCekFile = new File(getDeviceProtectedStorageDir(), Constants.WRAPPED_CEK_FILE);
        byte[] fileContent = null;
        try {
            // Read file content
            try (FileInputStream fis = new FileInputStream(wrappedCekFile)) {
                fileContent = new byte[(int)wrappedCekFile.length()];
                int bytesRead = fis.read(fileContent);
                if (bytesRead != fileContent.length) {
                    throw new IOException("File read incomplete");
                }
                Logger.d(TAG, "Key file content read, length: " + bytesRead + " bytes");
            }
            // Use file content directly as key
            byte[] keyBytes = fileContent;
            Logger.d(TAG, "Retrieved key (Base64): " + Base64.encodeToString(keyBytes, Constants.BASE64_NO_WRAP));
            Logger.d(TAG, "Using file content as key, length: " + keyBytes.length + " bytes");
            // Create CEK
            mAESKey = new SecretKeySpec(keyBytes, "AES");
            Logger.d(TAG, "AES key object created");
            // Put CEK into DirectByteBuffer for native layer use
            if (mAESKeyBuffer != null) {
                mAESKeyBuffer.clear();
                Logger.d(TAG, "Cleared old key buffer");
            }
            mAESKeyBuffer = ByteBuffer.allocateDirect(keyBytes.length);
            mAESKeyBuffer.put(keyBytes);
            mAESKeyBuffer.flip();
            Logger.d(TAG, "Created DirectByteBuffer for native layer use");
            // Clear key plaintext from temporary variables
            java.util.Arrays.fill(keyBytes, (byte) 0);
            Logger.d(TAG, "Cleared key plaintext from temporary variables");
        } finally {
            // Ensure file content is cleared
            if (fileContent != null) {
                java.util.Arrays.fill(fileContent, (byte) 0);
                Logger.d(TAG, "Cleared file content temporary variable");
            }
        }
    }
    /**
     * Gets AES key buffer.
     * 
     * @return Key buffer
     */
    public ByteBuffer getAESKeyBuffer() {
        return mAESKeyBuffer;
    }
    /**
     * Clears all key data.
     */
    public void clearKeys() {
        Logger.i(TAG, "Clearing all keys");
        if (mAESKeyBuffer != null) {
            mAESKeyBuffer.clear();
            mAESKeyBuffer = null;
            Logger.d(TAG, "Key buffer cleared");
        }
        mAESKey = null;
        Logger.d(TAG, "Key object in memory cleared");
    }
}