/*
* Copyright (C) 2025 THUNDERCOMM TECHNOLOGY Co.,Ltd.
* All Rights Reserved.
* Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
*/

package com.thundercomm.crown.fileencrypt;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import android.os.ParcelFileDescriptor;

import com.thundercomm.crown.fileencrypt.api.IFileEncryptServiceManager;
import com.thundercomm.crown.fileencrypt.utils.KeyManager;
import com.thundercomm.crown.fileencrypt.utils.NativeCrypto;
import com.thundercomm.crown.fileencrypt.utils.Logger;

/**
 * Service that handles file encryption and decryption operations.
 */
public class FileEncryptService extends Service {
    private static final String TAG = "FileEncryptService";

    private KeyManager mKeyManager;
    private NativeCrypto mNativeCrypto;

    @Override
    public void onCreate() {
        super.onCreate();
        Logger.i(TAG, "FileEncryptService is starting...");

        try {
            // Initialize key manager
            Logger.i(TAG, "Initializing key manager...");
            mKeyManager = new KeyManager(this, getFilesDir());
            mKeyManager.initialize();
            Logger.i(TAG, "Key manager initialization completed");

            // Initialize encryption processor
            Logger.i(TAG, "Initializing encryption processor...");
            try {
                mNativeCrypto = new NativeCrypto(mKeyManager);
                Logger.i(TAG, "Encryption processor initialization completed");
            } catch (Throwable exception) {
                Logger.e(TAG, "Encryption processor initialization failed, native library might be missing", exception);
                mNativeCrypto = null;
            }

            Logger.i(TAG, "FileEncryptService initialization successful");
        } catch (Exception exception) {
            Logger.e(TAG, "FileEncryptService initialization failed", exception);
            throw new RuntimeException("Failed to initialize service", exception);
        }
    }

    /**
     * Implementation of the AIDL interface for file encryption operations.
     */
    private final IFileEncryptServiceManager.Stub binder = new IFileEncryptServiceManager.Stub() {
        /**
         * Process a data stream for encryption or decryption.
         * 
         * @param isEncrypt Whether to encrypt (true) or decrypt (false) the data
         * @param inFd Input file descriptor for reading source data
         * @param outFd Output file descriptor for writing processed data
         * @return True if processing succeeds, false otherwise
         */
        @Override
        public boolean processStream(boolean isEncrypt, ParcelFileDescriptor inFd, ParcelFileDescriptor outFd) {
            Logger.i(TAG, String.format("Processing data stream: Encrypt=%b", isEncrypt));

            if (mNativeCrypto == null) {
                Logger.e(TAG, "Failed to process data stream: Native encryption library not loaded");
                return false;
            }

            long startTime = System.currentTimeMillis();

            try {
                // Get encryption key
                byte[] key = mNativeCrypto.getKeyBytes();
                if (key == null) {
                    Logger.e(TAG, "Unable to get encryption key");
                    return false;
                }

                // Generate IV (for encryption) or prepare to receive IV (for decryption)
                byte[] iv = isEncrypt ? mNativeCrypto.generateIV() : new byte[Constants.GCM_IV_LENGTH];

                // Get file descriptors
                int inputFd = inFd.getFd();
                int outputFd = outFd.getFd();

                Logger.d(TAG, String.format("Starting direct stream processing: InputFD=%d, OutputFD=%d",
                        inputFd, outputFd));

                boolean result = mNativeCrypto.processStream(isEncrypt, inputFd, outputFd, key, iv);
                float timeCost = (System.currentTimeMillis() - startTime) / (float) Constants.MILLIS_PER_SECOND;
                Logger.i(TAG, String.format("Stream processing %s, time cost: %.2f seconds",
                        result ? "succeeded" : "failed", timeCost));
                return result;

            } catch (Exception exception) {
                Logger.e(TAG, "Failed to process data stream", exception);
                return false;
            }
        }
    };

    /**
     * Return the binder implementation when a client binds to the service.
     *
     * @param intent The intent used to bind to the service
     * @return The binder interface for client communication
     */
    @Override
    public IBinder onBind(Intent intent) {
        Logger.i(TAG, "Service bound");
        return binder;
    }

    /**
     * Clean up resources when the service is destroyed.
     */
    @Override
    public void onDestroy() {
        Logger.i(TAG, "Service destroying, cleaning up resources");
        if (mKeyManager != null) {
            mKeyManager.clearKeys();
        }
        super.onDestroy();
        Logger.i(TAG, "Service destroyed");
    }
}