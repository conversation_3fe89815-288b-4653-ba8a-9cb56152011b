/*
* Copyright (C) 2025 THUNDERCOMM TECHNOLOGY Co.,Ltd.
* All Rights Reserved.
* Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
*/

package com.thundercomm.crown.fileencrypt.utils;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Build;
import android.provider.Settings;
import android.text.TextUtils;

/**
 * Device utility class, used to get unique device identifier.
 */
public class DeviceUtils {
    private static final String TAG = "DeviceUtils";
    private static final int MIN_ID_LENGTH_FOR_MASKING = 8;
    private static final int VISIBLE_CHARS_COUNT = 4;
    private static final String MASK_STARS = "****";

    /**
     * Get unique device identifier.
     * Prioritize using Android ID; if that fails, use a combination of device serial number and hardware information
     *
     * @param context Application context
     * @return Unique device identifier
     */
    @SuppressLint("HardwareIds")
    public static String getDeviceId(Context context) {
        String deviceId = null;

        // Try to get Android ID
        try {
            deviceId = Settings.Secure.getString(
                    context.getContentResolver(),
                    Settings.Secure.ANDROID_ID);
            Logger.d(TAG, "Got Android ID: " + maskId(deviceId));
        } catch (Exception exception) {
            Logger.e(TAG, "Failed to get Android ID", exception);
        }

        // If Android ID is empty or is a known unreliable value, use the alternative approach
        if (TextUtils.isEmpty(deviceId) || "9774d56d682e549c".equals(deviceId)) {
            Logger.d(TAG, "Android ID is invalid or a known unreliable value, will use device information combination");

            // Use device information combination
            Logger.d(TAG, "Device information details:");
            Logger.d(TAG, "- BOARD: " + Build.BOARD);
            Logger.d(TAG, "- BRAND: " + Build.BRAND);
            Logger.d(TAG, "- DEVICE: " + Build.DEVICE);
            Logger.d(TAG, "- HARDWARE: " + Build.HARDWARE);
            Logger.d(TAG, "- MANUFACTURER: " + Build.MANUFACTURER);
            Logger.d(TAG, "- MODEL: " + Build.MODEL);
            Logger.d(TAG, "- PRODUCT: " + Build.PRODUCT);

            final StringBuilder sb = new StringBuilder();
            sb.append(Build.BOARD).append("_")
                    .append(Build.BRAND).append("_")
                    .append(Build.DEVICE).append("_")
                    .append(Build.HARDWARE).append("_")
                    .append(Build.MANUFACTURER).append("_")
                    .append(Build.MODEL).append("_")
                    .append(Build.PRODUCT);

            // Add serial number (if available)
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
                Logger.d(TAG, "- SERIAL: " + maskId(Build.SERIAL));
                sb.append("_").append(Build.SERIAL);
            } else {
                try {
                    String serial = Build.getSerial();
                    Logger.d(TAG, "- SERIAL: " + maskId(serial));
                    if (!TextUtils.isEmpty(serial)) {
                        sb.append("_").append(serial);
                    }
                } catch (SecurityException exception) {
                    Logger.w(TAG, "Cannot get serial number, requires READ_PHONE_STATE permission");
                }
            }

            deviceId = sb.toString();

            // Ensure deviceId is not empty
            if (TextUtils.isEmpty(deviceId)) {
                deviceId = "unknown_device_" + System.currentTimeMillis();
                Logger.w(TAG, "Unable to get device information, using timestamp as device ID: " + deviceId);
            } else {
                Logger.d(TAG, "Generated device ID using device information combination: " + maskId(deviceId));
            }
        }

        Logger.i(TAG, "Final device ID: " + maskId(deviceId));
        return deviceId;
    }

    /**
     * Mask the ID, showing only the first 4 and last 4 characters, replacing the middle with asterisks.
     * Used for logging to protect privacy
     */
    private static String maskId(String id) {
        if (id == null || id.length() <= MIN_ID_LENGTH_FOR_MASKING) {
            return id;
        }

        return id.substring(0, VISIBLE_CHARS_COUNT) + MASK_STARS + id.substring(id.length() - VISIBLE_CHARS_COUNT);
    }
}