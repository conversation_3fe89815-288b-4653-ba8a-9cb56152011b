/*
 * Copyright (C) 2025 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 * All Rights Reserved.
 * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 */

package com.thundercomm.crown.fileencrypt;

import android.util.Base64;

/**
 * Constants for file encryption.
 */
public class Constants {
    // App info
    public static final String APP_VERSION = "1.0.0";

    // Base64 flags
    public static final int BASE64_NO_WRAP = Base64.NO_WRAP;

    // Size constants
    public static final int KB = 1024;
    public static final int MB = KB * KB;

    // Buffer sizes
    public static final int BUFFER_SIZE = 8 * KB; // 8KB
    public static final int CHUNK_SIZE = 4 * KB;
    public static final int LARGE_BUFFER_SIZE = 8 * KB;

    // Encryption algorithm
    public static final String ALGORITHM = "AES/GCM/NoPadding";
    public static final int AES_KEY_SIZE = 256; // bits
    public static final int GCM_IV_LENGTH = 12; // bytes
    public static final int GCM_TAG_LENGTH = 128; // bits

    // File names
    public static final String TEST_CONFIG_PREFIX = "test_config_";
    public static final String DECRYPTED_PREFIX = "decrypted_";
    
    // Time constants
    public static final int MILLIS_PER_SECOND = 1000;
    
    // Percentage constants
    public static final int PERCENTAGE_MAX = 100;

    // Key derivation parameters
    public static final int PBKDF2_ITERATIONS = 200;

    // File names and preferences
    public static final String PREFS_NAME = "FileEncryptPrefs";
    public static final String PREF_KEY_AES_KEY_GENERATED = "aes_key_generated";
    public static final String WRAPPED_CEK_FILE = "wrapped_cek.bin";
    public static final String KEK_KEY_ALIAS = "FileEncryptKEK";

    private Constants() {
        // Private constructor to prevent instantiation
    }
}