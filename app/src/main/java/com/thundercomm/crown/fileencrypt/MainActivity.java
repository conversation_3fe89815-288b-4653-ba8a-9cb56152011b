/*
* Copyright (C) 2025 THUNDERCOMM TECHNOLOGY Co.,Ltd.
* All Rights Reserved.
* Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
*/

package com.thundercomm.crown.fileencrypt;

import android.app.Activity;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.ParcelFileDescriptor;
import android.widget.Button;
import android.widget.TextView;

import com.thundercomm.crown.fileencrypt.api.FileEncryptServiceManager;
import com.thundercomm.crown.fileencrypt.api.IFileEncryptServiceConnectCallback;
import com.thundercomm.crown.fileencrypt.utils.Logger;

import java.io.File;
import java.io.FileOutputStream;
import java.io.FileInputStream;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;

/**
 * Main interface of the file encryption test tool.
 * This file is used for debugging only
 */
public class MainActivity extends Activity {
    private static final String TAG = "MainActivity";
    private static final int KB = 1024;
    private static final int MB = KB * KB;
    private TextView mStatusText;
    private FileEncryptServiceManager mServiceManager;
    private boolean mBound = false;
    private Handler mHandler = new Handler(Looper.getMainLooper());

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // Set title
        setTitle("File Encryption Test Tool v" + Constants.APP_VERSION);

        mStatusText = findViewById(R.id.status_text);
        Logger.setLogCallback(this::log);

        // Display welcome message
        log("Welcome to File Encryption Test Tool");
        log("Device model: " + Build.MODEL);
        log("Android version: " + Build.VERSION.RELEASE + " (API " + Build.VERSION.SDK_INT + ")");
        log("App version: " + Constants.APP_VERSION);
        log("-------------------");

        // Initialize test buttons
        initTestButtons();

        // Get FileEncryptServiceManager instance
        mServiceManager = FileEncryptServiceManager.getInstance();

        // Set service connection callback
        mServiceManager.setServiceConnectCallback(new IFileEncryptServiceConnectCallback() {
            @Override
            public void onServiceConnected() {
                mBound = true;
                Logger.log("Service connected");
            }

            @Override
            public void onServiceDisconnected() {
                mBound = false;
                Logger.log("Service disconnected");
            }
        });

        // Bind service
        mServiceManager.bindService(this);
    }

    /**
     * Check if the service is connected.
     *
     * @return true if service is connected, false otherwise
     */
    public boolean isServiceConnected() {
        if (!mBound || mServiceManager.isServiceDisconnected()) {
            Logger.log("Service not connected");
            return false;
        }
        return true;
    }

    /**
     * Initialize test buttons.
     */
    private void initTestButtons() {
        // Test stream encryption button
        Button testStreamEncryptBtn = findViewById(R.id.test_stream_encrypt_btn);
        testStreamEncryptBtn.setOnClickListener(v -> testStreamEncryption());

        // Test stream decryption button
        Button testStreamDecryptBtn = findViewById(R.id.test_stream_decrypt_btn);
        testStreamDecryptBtn.setOnClickListener(v -> testStreamDecryption());

        // Test video stream encryption button
        Button testVideoStreamEncryptBtn = findViewById(R.id.test_video_stream_encrypt_btn);
        testVideoStreamEncryptBtn.setOnClickListener(v -> testVideoStreamEncryption());

        // Test video stream decryption button
        Button testVideoStreamDecryptBtn = findViewById(R.id.test_video_stream_decrypt_btn);
        testVideoStreamDecryptBtn.setOnClickListener(v -> testVideoStreamDecryption());
    }

    /**
     * Log messages and update UI.
     * 
     * @param message Message to display
     */
    private void log(String message) {
        Logger.d(TAG, message);
        runOnUiThread(() -> {
            String currentText = mStatusText.getText().toString();
            mStatusText.setText(currentText + "\n" + message);
        });
    }

    /**
     * Test data stream encryption.
     */
    private void testStreamEncryption() {
        if (!isServiceConnected()) {
            return;
        }

        try {
            // Prepare multiple test file contents
            String[] testContents = {
                "This is a short text test",
                "This is a longer text test. It contains multiple sentences. "
                + "Testing encryption and decryption functionality.",
                "This is a very long text test.\n"
                + "It contains multiple lines.\n"
                + "Testing encryption and decryption of multi-line text.\n"
                + "Ensuring line breaks are handled correctly.\n"
                + "Contains special characters: !@#$%^&*()_+\n"
                + "Contains numbers: 1234567890\n"
                + "Contains English: The quick brown fox jumps over the lazy dog.",
                // JSON format test data
                "{\n"
                + "    \"name\": \"Test Configuration\",\n"
                + "    \"version\": \"1.0.0\",\n"
                + "    \"settings\": {\n"
                + "        \"enabled\": true,\n"
                + "        \"timeout\": 30000,\n"
                + "        \"retries\": 3\n"
                + "    }\n"
                + "}"
            };

            // Create encrypted file for each test content
            for (int i = 0; i < testContents.length; i++) {
                String content = testContents[i];
                String fileName = String.format(Constants.TEST_CONFIG_PREFIX + "%d.bin", i + 1);
                File encryptedFile = new File(getExternalFilesDir(null), fileName);

                // Create input/output file descriptors
                File tempInputFile = File.createTempFile("temp_input", ".txt", getCacheDir());
                try (FileOutputStream fos = new FileOutputStream(tempInputFile)) {
                    fos.write(content.getBytes(StandardCharsets.UTF_8));
                }

                ParcelFileDescriptor inFd = ParcelFileDescriptor.open(tempInputFile,
                        ParcelFileDescriptor.MODE_READ_ONLY);
                ParcelFileDescriptor outFd = ParcelFileDescriptor.open(encryptedFile,
                    ParcelFileDescriptor.MODE_CREATE | ParcelFileDescriptor.MODE_WRITE_ONLY);

                // Use ServiceManager to perform encryption
                final boolean success = mServiceManager.processStream(true, inFd, outFd);

                // Close file descriptors
                inFd.close();
                outFd.close();
                tempInputFile.delete();

                if (success) {
                    Logger.log(String.format("File %s encrypted successfully, size: %d bytes",
                            fileName, encryptedFile.length()));
                } else {
                    Logger.log(String.format("File %s encryption failed", fileName));
                }
            }

            Logger.log("All test files encrypted successfully");

        } catch (Exception exception) {
            Logger.e(TAG, "Stream encryption test failed", exception);
        }
    }

    /**
     * Test data stream decryption.
     */
    private void testStreamDecryption() {
        if (!isServiceConnected()) {
            return;
        }

        try {
            StringBuilder resultBuilder = new StringBuilder();
            resultBuilder.append("Decryption Results:\n\n");

            // Get all encrypted test files
            File[] encryptedFiles = getExternalFilesDir(null).listFiles((dir, name) ->
                    name.startsWith(Constants.TEST_CONFIG_PREFIX) && name.endsWith(".bin"));

            if (encryptedFiles == null || encryptedFiles.length == 0) {
                Logger.log("No encrypted test files found, please run encryption test first");
                return;
            }

            // Decrypt each file
            for (File encryptedFile : encryptedFiles) {
                // Create temporary output file
                File decryptedFile = File.createTempFile(Constants.DECRYPTED_PREFIX, ".txt", getCacheDir());

                // Create file descriptors
                ParcelFileDescriptor inFd = ParcelFileDescriptor.open(encryptedFile,
                        ParcelFileDescriptor.MODE_READ_ONLY);
                ParcelFileDescriptor outFd = ParcelFileDescriptor.open(decryptedFile,
                        ParcelFileDescriptor.MODE_CREATE | ParcelFileDescriptor.MODE_WRITE_ONLY);

                // Use ServiceManager to perform decryption
                final boolean success = mServiceManager.processStream(false, inFd, outFd);

                // Close file descriptors
                inFd.close();
                outFd.close();

                if (success) {
                    // Read decrypted content
                    byte[] decryptedBytes = new byte[(int) decryptedFile.length()];
                    try (FileInputStream fis = new FileInputStream(decryptedFile)) {
                        fis.read(decryptedBytes);
                    }
                    String decryptedContent = new String(decryptedBytes, StandardCharsets.UTF_8);

                    // Add to results
                    resultBuilder.append("File: ").append(encryptedFile.getName()).append("\n");
                    resultBuilder.append("Content:\n").append(decryptedContent).append("\n\n");
                    Logger.log(String.format("File %s decrypted successfully", encryptedFile.getName()));
                } else {
                    resultBuilder.append("File: ").append(encryptedFile.getName())
                            .append(" decryption failed\n\n");
                    Logger.log(String.format("File %s decryption failed", encryptedFile.getName()));
                }

                // Delete temporary file
                decryptedFile.delete();
            }

            // Display all decryption results in UI
            Logger.log(resultBuilder.toString());

        } catch (Exception exception) {
            Logger.e(TAG, "Stream decryption test failed", exception);
        }
    }

    /**
     * Test video stream encryption.
     */
    private void testVideoStreamEncryption() {
        if (!isServiceConnected()) {
            return;
        }

        new VideoProcessingTask(true).execute();
    }

    /**
     * Test video stream decryption.
     */
    private void testVideoStreamDecryption() {
        if (!isServiceConnected()) {
            return;
        }

        new VideoProcessingTask(false).execute();
    }

    /**
     * Video processing task for encrypting or decrypting large files.
     */
    private class VideoProcessingTask extends AsyncTask<Void, Integer, Boolean> {
        private final boolean isEncrypting;
        private long startTime;
        private File sourceFile;
        private File destFile;
        private double fileSize;

        public VideoProcessingTask(boolean isEncrypting) {
            this.isEncrypting = isEncrypting;
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            setButtonsEnabled(false);
            startTime = System.currentTimeMillis();

            Logger.log(isEncrypting ? "Starting video encryption test..." : "Starting video decryption test...");

            // Create test file if encrypting
            if (isEncrypting) {
                sourceFile = new File(getCacheDir(), "test_video.raw");
                destFile = new File(getExternalFilesDir(null), "encrypted_video.bin");
                // Create a test file (50MB)
                final long testFileSize = 50 * MB; // 50MB
                Logger.log("Creating test file of size: " + (testFileSize / MB) + "MB");
                try {
                    createTestFile(sourceFile, testFileSize);
                } catch (Exception exception) {
                    Logger.e(TAG, "Failed to create test file", exception);
                    cancel(true);
                }
            } else {
                sourceFile = new File(getExternalFilesDir(null), "encrypted_video.bin");
                destFile = new File(getCacheDir(), "decrypted_video.raw");
                if (!sourceFile.exists()) {
                    Logger.log("No encrypted video file found, please run encryption test first");
                    cancel(true);
                }
            }
            fileSize = sourceFile.length() / (double) MB; // in MB
        }

        /**
         * Creates test file.
         * 
         * @param file File to create
         * @param size File size in bytes
         * @throws Exception Thrown when file creation fails
         */
        private void createTestFile(File file, long size) throws Exception {
            try (FileOutputStream fos = new FileOutputStream(file)) {
                byte[] buffer = new byte[Constants.BUFFER_SIZE];
                new SecureRandom().nextBytes(buffer);

                long remaining = size;
                while (remaining > 0) {
                    int toWrite = (int) Math.min(buffer.length, remaining);
                    fos.write(buffer, 0, toWrite);
                    remaining -= toWrite;

                    // Report progress
                    int progress = (int) (Constants.PERCENTAGE_MAX - (remaining * Constants.PERCENTAGE_MAX / size));
                    publishProgress(progress);
                }
                fos.getFD().sync();
            }
            Logger.log("Test file created: " + file.length() + " bytes");
        }

        @Override
        protected Boolean doInBackground(Void... params) {
            try {
                Logger.log("Processing " + fileSize + "MB file...");

                ParcelFileDescriptor inFd = ParcelFileDescriptor.open(sourceFile,
                        ParcelFileDescriptor.MODE_READ_ONLY);
                ParcelFileDescriptor outFd = ParcelFileDescriptor.open(destFile,
                    ParcelFileDescriptor.MODE_CREATE | ParcelFileDescriptor.MODE_WRITE_ONLY);

                boolean success = mServiceManager.processStream(isEncrypting, inFd, outFd);
                inFd.close();
                outFd.close();

                return success;
            } catch (Exception exception) {
                Logger.e(TAG, "Video processing failed", exception);
                return false;
            }
        }

        @Override
        protected void onProgressUpdate(Integer... values) {
            super.onProgressUpdate(values);
            // Logger.log("Progress: " + values[0] + "%");
        }

        @Override
        protected void onPostExecute(Boolean success) {
            super.onPostExecute(success);
            long timeTaken = System.currentTimeMillis() - startTime;
            double seconds = timeTaken / (double) Constants.MILLIS_PER_SECOND;
            double mbPerSec = fileSize / seconds;

            if (success) {
                Logger.log(String.format("%s completed successfully", isEncrypting ? "Encryption" : "Decryption"));
                Logger.log(String.format("Time taken: %.2f seconds (%.2f MB/s)", seconds, mbPerSec));
                Logger.log(String.format("Output file size: %.2f MB", destFile.length() / (double) MB));
            } else {
                Logger.log(String.format("%s failed", isEncrypting ? "Encryption" : "Decryption"));
            }

            setButtonsEnabled(true);
        }
    }

    /**
     * Sets enabled state for all buttons.
     * 
     * @param enabled Whether buttons should be enabled
     */
    private void setButtonsEnabled(boolean enabled) {
        runOnUiThread(() -> {
            findViewById(R.id.test_stream_encrypt_btn).setEnabled(enabled);
            findViewById(R.id.test_stream_decrypt_btn).setEnabled(enabled);
            findViewById(R.id.test_video_stream_encrypt_btn).setEnabled(enabled);
            findViewById(R.id.test_video_stream_decrypt_btn).setEnabled(enabled);
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mServiceManager != null) {
            mServiceManager.unbindService(this);
        }
    }
}