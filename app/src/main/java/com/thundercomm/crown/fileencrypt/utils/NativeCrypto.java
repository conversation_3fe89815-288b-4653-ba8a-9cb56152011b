/*
* Copyright (C) 2025 THUNDERCOMM TECHNOLOGY Co.,Ltd.
* All Rights Reserved.
* Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
*/
package com.thundercomm.crown.fileencrypt.utils;
import android.util.Base64;
import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.SecureRandom;
import java.nio.ByteBuffer;
import com.thundercomm.crown.fileencrypt.Constants;
/**
 * Provides interface for native encryption operations.
 */
public class NativeCrypto {
    private static final String TAG = "FileEncrypt_NativeCrypto";
    static {
        try {
            System.loadLibrary("fileencrypt");
            Logger.i(TAG, "Native crypto library 'fileencrypt' loaded successfully.");
        } catch (UnsatisfiedLinkError exception) {
            Logger.e(TAG, "Failed to load 'fileencrypt' via loadLibrary, trying absolute path.", exception);
        }
    }
    // Native method declarations
    private static native boolean processStream(int inputFd, int outputFd,
                                                byte[] key, byte[] iv, boolean encrypt);
    // Instance variables
    private final KeyManager mKeyManager;
    private final SecureRandom mSecureRandom;
    private byte[] mCurrentIV;
    private Cipher mCurrentCipher;  // Store current Cipher instance
    /**
     * Creates NativeCrypto instance.
     * 
     * @param keyManager Key manager instance
     */
    public NativeCrypto(KeyManager keyManager) {
        mKeyManager = keyManager;
        mSecureRandom = new SecureRandom();
    }
    /**
     * Gets key byte array.
     * 
     * @return Key byte array, or null if no key available
     */
    public byte[] getKeyBytes() {
        ByteBuffer keyBuffer = mKeyManager.getAESKeyBuffer();
        if (keyBuffer == null) {
            Logger.e(TAG, "No encryption key available");
            return null;
        }
        // Reset buffer position to beginning
        keyBuffer.position(0);
        byte[] key = new byte[keyBuffer.capacity()];
        keyBuffer.get(key);
        Logger.d(TAG, "Get AES key (Base64): " + Base64.encodeToString(key, Constants.BASE64_NO_WRAP));
        return key;
    }
    /**
     * Generates a new initialization vector (IV).
     * 
     * @return Newly generated IV byte array
     */
    public byte[] generateIV() {
        byte[] iv = new byte[Constants.GCM_IV_LENGTH];
        mSecureRandom.nextBytes(iv);
        mCurrentIV = iv;
        Logger.d(TAG, "Generated new IV: " + Base64.encodeToString(iv, Constants.BASE64_NO_WRAP));
        return iv;
    }
    /**
     * Processes data stream.
     * 
     * @param isEncrypt Whether this is an encryption operation
     * @param inputFd Input file descriptor
     * @param outputFd Output file descriptor
     * @param key Encryption key
     * @param iv Initialization vector
     * @return Whether operation succeeded
     */
    public boolean processStream(boolean isEncrypt, int inputFd, int outputFd, byte[] key, byte[] iv) {
        try {
            Logger.d(TAG, String.format("Direct stream processing: Encrypt=%b, InputFD=%d, OutputFD=%d",
                    isEncrypt, inputFd, outputFd));
            if (key == null || iv == null) {
                Logger.e(TAG, "Key or IV is null");
                return false;
            }
            return processStream(inputFd, outputFd, key, iv, isEncrypt);
        } catch (Exception exception) {
            Logger.e(TAG, "Failed to process stream", exception);
            return false;
        } finally {
            // Clear key from memory
            if (key != null) {
                java.util.Arrays.fill(key, (byte) 0);
            }
        }
    }
    /**
     * Gets stream encryptor.
     * 
     * @return Configured Cipher object
     * @throws Exception Thrown when initializing encryptor fails
     */
    public Cipher getStreamEncryptor() throws Exception {
        // Generate new IV
        mCurrentIV = new byte[Constants.GCM_IV_LENGTH];
        mSecureRandom.nextBytes(mCurrentIV);
        Logger.d(TAG, "Generated new IV: " + Base64.encodeToString(mCurrentIV, Constants.BASE64_NO_WRAP));
        // Get key
        ByteBuffer keyBuffer = mKeyManager.getAESKeyBuffer();
        keyBuffer.position(0);
        byte[] key = new byte[keyBuffer.capacity()];
        keyBuffer.get(key);
        Logger.d(TAG, "Encryption key (Base64): " + Base64.encodeToString(key, Constants.BASE64_NO_WRAP));
        try {
            // Create new Cipher instance
            mCurrentCipher = Cipher.getInstance("AES/GCM/NoPadding");
            SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
            GCMParameterSpec paramSpec = new GCMParameterSpec(Constants.GCM_TAG_LENGTH, mCurrentIV);
            mCurrentCipher.init(Cipher.ENCRYPT_MODE, keySpec, paramSpec);
            // Add IV as AAD to authentication data
            mCurrentCipher.updateAAD(mCurrentIV);
            Logger.d(TAG, "Encryptor initialized, IV added as AAD");
            return mCurrentCipher;
        } finally {
            java.util.Arrays.fill(key, (byte) 0);
        }
    }
    /**
     * Gets stream decryptor.
     * 
     * @return Configured Cipher object
     * @throws Exception Thrown when initializing decryptor fails
     */
    public Cipher getStreamDecryptor() throws Exception {
        if (mCurrentCipher == null) {
            // First call, create new Cipher instance
            mCurrentCipher = Cipher.getInstance("AES/GCM/NoPadding");
            Logger.d(TAG, "Get decryptor - waiting for IV initialization");
        }
        return mCurrentCipher;
    }
    /**
     * Initializes stream decryptor.
     * 
     * @param data Data containing IV
     * @param offset Offset of IV in data
     * @param length Data length
     * @throws Exception Thrown when initialization fails
     */
    public void initializeStreamDecryptor(byte[] data, int offset, int length) throws Exception {
        if (length < Constants.GCM_IV_LENGTH) {
            throw new IllegalArgumentException("Data length too short to contain IV");
        }
        // Save IV
        mCurrentIV = new byte[Constants.GCM_IV_LENGTH];
        System.arraycopy(data, offset, mCurrentIV, 0, Constants.GCM_IV_LENGTH);
        Logger.d(TAG, "Decryption IV: " + Base64.encodeToString(mCurrentIV, Constants.BASE64_NO_WRAP));
        // Get key
        ByteBuffer keyBuffer = mKeyManager.getAESKeyBuffer();
        keyBuffer.position(0);
        byte[] key = new byte[keyBuffer.capacity()];
        keyBuffer.get(key);
        Logger.d(TAG, "Decryption key (Base64): " + Base64.encodeToString(key, Constants.BASE64_NO_WRAP));
        try {
            // Initialize current Cipher instance
            SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
            GCMParameterSpec paramSpec = new GCMParameterSpec(Constants.GCM_TAG_LENGTH, mCurrentIV);
            mCurrentCipher.init(Cipher.DECRYPT_MODE, keySpec, paramSpec);
            // Add IV as AAD to authentication data
            mCurrentCipher.updateAAD(mCurrentIV);
            Logger.d(TAG, "Decryptor initialized, IV added as AAD");
        } finally {
            java.util.Arrays.fill(key, (byte) 0);
        }
    }
    /**
     * Gets the current encryptor's IV.
     * 
     * @return IV byte array
     */
    public byte[] getEncryptorIV() {
        return mCurrentIV;
    }
}