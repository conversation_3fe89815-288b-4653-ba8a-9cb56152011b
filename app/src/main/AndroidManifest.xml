<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.thundercomm.crown.fileencrypt">

    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <application
        android:allowBackup="true"
        android:persistent="true"
        android:directBootAware="true"
        android:theme="@style/AppTheme">
 
        <activity
            android:name=".MainActivity"
            android:exported="true">
        </activity>

        <service
            android:name=".FileEncryptService"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.thundercomm.crown.fileencrypt.IFileEncryptService" />
            </intent-filter>
        </service>

    </application>

</manifest>