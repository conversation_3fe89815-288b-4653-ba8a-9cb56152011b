plugins {
    id 'com.android.application'
}

android {
    namespace 'com.thundercomm.crown.fileencrypt'
    compileSdk 33

    defaultConfig {
        applicationId "com.thundercomm.crown.fileencrypt"
        minSdk 29
        targetSdk 33
        versionCode 1
        versionName "1.0"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        ndk {
            abiFilters 'arm64-v8a'
        }

        externalNativeBuild {
            cmake {
                cppFlags "-std=c++11"
                arguments "-DANDROID_STL=c++_shared"
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    externalNativeBuild {
        cmake {
            path file('src/main/cpp/CMakeLists.txt')
            version '3.22.1'
        }
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['src/main/jniLibs', 'libs']
        }
    }

    // 创建目录结构以便手动添加OpenSSL库
    task createLibsDirs {
        doLast {
            new File("${projectDir}/libs/arm64-v8a").mkdirs()
        }
    }

    preBuild.dependsOn(createLibsDirs)
    buildToolsVersion '34.0.0'
    ndkVersion '23.2.8568313'

    configurations.all {
        resolutionStrategy {
            force 'org.jetbrains.kotlin:kotlin-stdlib:1.8.10'
            force 'org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.10'
            force 'org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.10'
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.9.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    implementation project(':fileencryptinterface')
}