{"logs": [{"outputFile": "com.thundercomm.crown.fileencrypt.test.app-mergeDebugAndroidTestResources-4:/values-v21/values-v21.xml", "map": [{"source": "/home/<USER>/.gradle/caches/transforms-3/68f57515f307e72238dbbf2c94c76bf2/transformed/core-1.5.0/res/values-v21/values.xml", "from": {"startLines": "4,13", "startColumns": "0,0", "startOffsets": "180,655", "endLines": "12,21", "endColumns": "8,8", "endOffsets": "654,1127"}, "to": {"startLines": "2,11", "startColumns": "4,4", "startOffsets": "55,534", "endLines": "10,19", "endColumns": "8,8", "endOffsets": "529,1006"}}]}]}