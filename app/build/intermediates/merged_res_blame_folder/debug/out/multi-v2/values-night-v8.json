{"logs": [{"outputFile": "com.thundercomm.crown.fileencrypt.app-mergeDebugResources-27:/values-night-v8/values-night-v8.xml", "map": [{"source": "/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/res/values-night/themes.xml", "from": {"startLines": "10", "startColumns": "4", "startOffsets": "323", "endLines": "18", "endColumns": "12", "endOffsets": "789"}, "to": {"startLines": "9", "startColumns": "4", "startOffsets": "687", "endLines": "17", "endColumns": "12", "endOffsets": "1063"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/058cfc4da2ae93637a16a1362856d4c0/transformed/material-1.9.0/res/values-night-v8/values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,851,954,1042,1166,1268,1370,1486,1588,1702,1830,1946,2068,2204,2324,2458,2578,2690,2816,2933,3057,3187,3309,3447,3581,3697", "endColumns": "74,110,88,100,106,106,98,106,102,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,846,949,1037,1161,1263,1365,1481,1583,1697,1825,1941,2063,2199,2319,2453,2573,2685,2811,2928,3052,3182,3304,3442,3576,3692,3812"}, "to": {"startLines": "18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,44,45,46,47,48,49,50,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1068,1143,1254,1343,1444,1551,1658,1757,1864,1967,2055,2179,2281,2383,2499,2601,2715,2843,2959,3081,3217,3337,3471,3591,3703,3918,4035,4159,4289,4411,4549,4683,4799", "endColumns": "74,110,88,100,106,106,98,106,102,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "1138,1249,1338,1439,1546,1653,1752,1859,1962,2050,2174,2276,2378,2494,2596,2710,2838,2954,3076,3212,3332,3466,3586,3698,3824,4030,4154,4284,4406,4544,4678,4794,4914"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/452736d1d908cf94baf2f23bc48e1513/transformed/appcompat-1.6.1/res/values-night-v8/values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "2,3,4,5,6,7,8,43", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,3829", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,3913"}}]}]}