[{"merged": "com.thundercomm.crown.fileencrypt.app-merged_res-29:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.thundercomm.crown.fileencrypt.app-main-31:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.thundercomm.crown.fileencrypt.app-merged_res-29:/mipmap-anydpi_ic_launcher.xml.flat", "source": "com.thundercomm.crown.fileencrypt.app-main-31:/mipmap-anydpi/ic_launcher.xml"}, {"merged": "com.thundercomm.crown.fileencrypt.app-merged_res-29:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.thundercomm.crown.fileencrypt.app-main-31:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.thundercomm.crown.fileencrypt.app-merged_res-29:/mipmap-anydpi_ic_launcher_round.xml.flat", "source": "com.thundercomm.crown.fileencrypt.app-main-31:/mipmap-anydpi/ic_launcher_round.xml"}, {"merged": "com.thundercomm.crown.fileencrypt.app-merged_res-29:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.thundercomm.crown.fileencrypt.app-main-31:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.thundercomm.crown.fileencrypt.app-merged_res-29:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.thundercomm.crown.fileencrypt.app-main-31:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.thundercomm.crown.fileencrypt.app-merged_res-29:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.thundercomm.crown.fileencrypt.app-main-31:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.thundercomm.crown.fileencrypt.app-merged_res-29:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.thundercomm.crown.fileencrypt.app-main-31:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.thundercomm.crown.fileencrypt.app-merged_res-29:/xml_backup_rules.xml.flat", "source": "com.thundercomm.crown.fileencrypt.app-main-31:/xml/backup_rules.xml"}, {"merged": "com.thundercomm.crown.fileencrypt.app-merged_res-29:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.thundercomm.crown.fileencrypt.app-main-31:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.thundercomm.crown.fileencrypt.app-merged_res-29:/xml_data_extraction_rules.xml.flat", "source": "com.thundercomm.crown.fileencrypt.app-main-31:/xml/data_extraction_rules.xml"}, {"merged": "com.thundercomm.crown.fileencrypt.app-merged_res-29:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.thundercomm.crown.fileencrypt.app-main-31:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.thundercomm.crown.fileencrypt.app-merged_res-29:/layout_activity_main.xml.flat", "source": "com.thundercomm.crown.fileencrypt.app-main-31:/layout/activity_main.xml"}, {"merged": "com.thundercomm.crown.fileencrypt.app-merged_res-29:/layout_main_activity.xml.flat", "source": "com.thundercomm.crown.fileencrypt.app-main-31:/layout/main_activity.xml"}, {"merged": "com.thundercomm.crown.fileencrypt.app-merged_res-29:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.thundercomm.crown.fileencrypt.app-main-31:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.thundercomm.crown.fileencrypt.app-merged_res-29:/drawable_ic_launcher_foreground.xml.flat", "source": "com.thundercomm.crown.fileencrypt.app-main-31:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.thundercomm.crown.fileencrypt.app-merged_res-29:/drawable_ic_launcher_background.xml.flat", "source": "com.thundercomm.crown.fileencrypt.app-main-31:/drawable/ic_launcher_background.xml"}, {"merged": "com.thundercomm.crown.fileencrypt.app-merged_res-29:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.thundercomm.crown.fileencrypt.app-main-31:/mipmap-xhdpi/ic_launcher_round.webp"}]