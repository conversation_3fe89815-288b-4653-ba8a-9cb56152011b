<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#FF000000</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="white">#FFFFFFFF</color>
    <item name="test_stream_decrypt_btn" type="id"/>
    <item name="test_stream_encrypt_btn" type="id"/>
    <string name="app_name">File Encryption</string>
    <string name="status_title">Status Log:</string>
    <string name="test_stream_decrypt">Test Stream Decryption</string>
    <string name="test_stream_encrypt">Test Stream Encryption</string>
    <string name="test_video_stream_decrypt">Video Stream Decryption</string>
    <string name="test_video_stream_encrypt">Video Stream Encryption</string>
    <style name="AppTheme" parent="android:Theme.Holo.Light.DarkActionBar">
        
        <item name="android:colorPrimary">@color/purple_500</item>
        <item name="android:colorPrimaryDark">@color/purple_700</item>
        <item name="android:colorAccent">@color/teal_200</item>
    </style>
    <style name="Theme.CrownFileEncrypt" parent="android:Theme.Holo.Light.DarkActionBar">
        
        <item name="android:colorPrimary">@color/purple_500</item>
        <item name="android:colorPrimaryDark">@color/purple_700</item>
        <item name="android:colorAccent">@color/teal_200</item>
        
        <item name="android:statusBarColor">@color/purple_700</item>
        
    </style>
</resources>