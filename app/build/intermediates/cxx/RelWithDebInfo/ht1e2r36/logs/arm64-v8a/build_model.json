{"abi": "ARM64_V8A", "info": {"abi": "ARM64_V8A", "bitness": 64, "deprecated": false, "default": true}, "cxxBuildFolder": "/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/.cxx/RelWithDebInfo/ht1e2r36/arm64-v8a", "soFolder": "/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/build/intermediates/cxx/RelWithDebInfo/ht1e2r36/obj/arm64-v8a", "soRepublishFolder": "/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/build/intermediates/cmake/release/obj/arm64-v8a", "abiPlatformVersion": 29, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": ["-DANDROID_STL=c++_shared"], "cFlagsList": [], "cppFlagsList": ["-std=c++11"], "variantName": "release", "isDebuggableEnabled": false, "validAbiList": ["ARM64_V8A"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/.cxx", "intermediatesBaseFolder": "/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/build/intermediates", "intermediatesFolder": "/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/build/intermediates/cxx", "gradleModulePathName": ":app", "moduleRootFolder": "/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app", "moduleBuildFile": "/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/build.gradle", "makeFile": "/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/cpp/CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "/opt/sdk/ndk/23.2.8568313", "ndkVersion": "23.2.8568313", "ndkSupportedAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "ndkDefaultAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 16, "max": 31, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31}}, "ndkMetaAbiList": [{"abi": "ARMEABI_V7A", "bitness": 32, "deprecated": false, "default": true}, {"abi": "ARM64_V8A", "bitness": 64, "deprecated": false, "default": true}, {"abi": "X86", "bitness": 32, "deprecated": false, "default": true}, {"abi": "X86_64", "bitness": 64, "deprecated": false, "default": true}], "cmakeToolchainFile": "/opt/sdk/ndk/23.2.8568313/build/cmake/android.toolchain.cmake", "cmake": {"isValidCmakeAvailable": true, "cmakeExe": "/opt/sdk/cmake/3.22.1/bin/cmake", "minimumCmakeVersion": "3.22.1"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"ARMEABI_V7A": "/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/arm-linux-androideabi/libc++_shared.so", "ARM64_V8A": "/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/libc++_shared.so", "X86": "/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/i686-linux-android/libc++_shared.so", "X86_64": "/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/x86_64-linux-android/libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt", "sdkFolder": "/opt/sdk", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": false}, "outputOptions": [], "ninjaExe": "/opt/sdk/cmake/3.22.1/bin/ninja"}, "prefabClassPaths": [], "prefabPackages": [], "prefabPackageConfigurations": [], "stlType": "c++_shared", "optimizationTag": "RelWithDebInfo"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/.cxx/RelWithDebInfo/ht1e2r36/prefab/arm64-v8a", "isActiveAbi": true, "fullConfigurationHash": "ht1e2r363i412e2e681e5a1r1x5e4l1j2u5647196d1m3k734a4ygb55403f", "configurationArguments": ["-H/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/cpp", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=29", "-DANDROID_PLATFORM=android-29", "-DANDROID_ABI=arm64-v8a", "-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a", "-DANDROID_NDK=/opt/sdk/ndk/23.2.8568313", "-DCMAKE_ANDROID_NDK=/opt/sdk/ndk/23.2.8568313", "-DCMAKE_TOOLCHAIN_FILE=/opt/sdk/ndk/23.2.8568313/build/cmake/android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=/opt/sdk/cmake/3.22.1/bin/ninja", "-DCMAKE_CXX_FLAGS=-std=c++11", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/build/intermediates/cxx/RelWithDebInfo/ht1e2r36/obj/arm64-v8a", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/build/intermediates/cxx/RelWithDebInfo/ht1e2r36/obj/arm64-v8a", "-DCMAKE_BUILD_TYPE=RelWithDebInfo", "-B/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/.cxx/RelWithDebInfo/ht1e2r36/arm64-v8a", "-<PERSON><PERSON><PERSON><PERSON>", "-DANDROID_STL=c++_shared"], "stlLibraryFile": "/opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/sysroot/usr/lib/aarch64-linux-android/libc++_shared.so", "intermediatesParentFolder": "/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/build/intermediates/cxx/RelWithDebInfo/ht1e2r36"}