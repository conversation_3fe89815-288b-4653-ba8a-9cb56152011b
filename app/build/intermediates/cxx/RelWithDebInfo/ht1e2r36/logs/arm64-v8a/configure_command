/opt/sdk/cmake/3.22.1/bin/cmake \
  -H/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/cpp \
  -DCMAKE_SYSTEM_NAME=Android \
  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
  -DCMAKE_SYSTEM_VERSION=29 \
  -DANDROID_PLATFORM=android-29 \
  -DANDROID_ABI=arm64-v8a \
  -DCMAKE_ANDROID_ARCH_ABI=arm64-v8a \
  -DANDROID_NDK=/opt/sdk/ndk/23.2.8568313 \
  -DCMAKE_ANDROID_NDK=/opt/sdk/ndk/23.2.8568313 \
  -DCMAKE_TOOLCHAIN_FILE=/opt/sdk/ndk/23.2.8568313/build/cmake/android.toolchain.cmake \
  -DCMAKE_MAKE_PROGRAM=/opt/sdk/cmake/3.22.1/bin/ninja \
  -DCMAKE_CXX_FLAGS=-std=c++11 \
  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/build/intermediates/cxx/RelWithDebInfo/ht1e2r36/obj/arm64-v8a \
  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/build/intermediates/cxx/RelWithDebInfo/ht1e2r36/obj/arm64-v8a \
  -DCMAKE_BUILD_TYPE=RelWithDebInfo \
  -B/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/.cxx/RelWithDebInfo/ht1e2r36/arm64-v8a \
  -GNinja \
  -DANDROID_STL=c++_shared
