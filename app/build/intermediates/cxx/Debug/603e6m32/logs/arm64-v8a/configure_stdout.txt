-- The C compiler identification is Clang 12.0.9
-- The CXX compiler identification is Clang 12.0.9
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler AB<PERSON> info - done
-- Check for working C compiler: /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/bin/clang - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Configuring incomplete, errors occurred!
See also "/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/.cxx/Debug/603e6m32/arm64-v8a/CMakeFiles/CMakeOutput.log".
