[{"level_": 0, "message_": "Start JSON generation. Platform version: 29 min SDK version: arm64-v8a", "file_": "/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/cpp/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1366261856}, {"level_": 0, "message_": "rebuilding JSON /media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/.cxx/Debug/603e6m32/arm64-v8a/android_gradle_build.json due to:", "file_": "/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/cpp/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -784425}, {"level_": 0, "message_": "- expected json /media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/.cxx/Debug/603e6m32/arm64-v8a/android_gradle_build.json file is not present, will remove stale json folder", "file_": "/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/cpp/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1257570051}, {"level_": 0, "message_": "- missing previous command file /media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/.cxx/Debug/603e6m32/arm64-v8a/metadata_generation_command.txt, will remove stale json folder", "file_": "/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/cpp/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -8829061}, {"level_": 0, "message_": "- command changed from previous, will remove stale json folder", "file_": "/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/cpp/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -147325130}, {"level_": 0, "message_": "removing stale contents from '/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/.cxx/Debug/603e6m32/arm64-v8a'", "file_": "/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/cpp/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1871001290}, {"level_": 0, "message_": "created folder '/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/.cxx/Debug/603e6m32/arm64-v8a'", "file_": "/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/cpp/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1784558142}, {"level_": 0, "message_": "executing cmake /opt/sdk/cmake/3.22.1/bin/cmake \\\n  -H/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/cpp \\\n  -DCMAKE_SYSTEM_NAME=Android \\\n  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \\\n  -DCMAKE_SYSTEM_VERSION=29 \\\n  -DANDROID_PLATFORM=android-29 \\\n  -DANDROID_ABI=arm64-v8a \\\n  -DCMAKE_ANDROID_ARCH_ABI=arm64-v8a \\\n  -DANDROID_NDK=/opt/sdk/ndk/23.2.8568313 \\\n  -DCMAKE_ANDROID_NDK=/opt/sdk/ndk/23.2.8568313 \\\n  -DCMAKE_TOOLCHAIN_FILE=/opt/sdk/ndk/23.2.8568313/build/cmake/android.toolchain.cmake \\\n  -DCMAKE_MAKE_PROGRAM=/opt/sdk/cmake/3.22.1/bin/ninja \\\n  -DCMAKE_CXX_FLAGS=-std=c++11 \\\n  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/build/intermediates/cxx/Debug/603e6m32/obj/arm64-v8a \\\n  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/build/intermediates/cxx/Debug/603e6m32/obj/arm64-v8a \\\n  -DCMAKE_BUILD_TYPE=Debug \\\n  -B/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/.cxx/Debug/603e6m32/arm64-v8a \\\n  -GNinja \\\n  -DANDROID_STL=c++_shared\n", "file_": "/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/cpp/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -151421614}, {"level_": 0, "message_": "/opt/sdk/cmake/3.22.1/bin/cmake \\\n  -H/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/cpp \\\n  -DCMAKE_SYSTEM_NAME=Android \\\n  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \\\n  -DCMAKE_SYSTEM_VERSION=29 \\\n  -DANDROID_PLATFORM=android-29 \\\n  -DANDROID_ABI=arm64-v8a \\\n  -DCMAKE_ANDROID_ARCH_ABI=arm64-v8a \\\n  -DANDROID_NDK=/opt/sdk/ndk/23.2.8568313 \\\n  -DCMAKE_ANDROID_NDK=/opt/sdk/ndk/23.2.8568313 \\\n  -DCMAKE_TOOLCHAIN_FILE=/opt/sdk/ndk/23.2.8568313/build/cmake/android.toolchain.cmake \\\n  -DCMAKE_MAKE_PROGRAM=/opt/sdk/cmake/3.22.1/bin/ninja \\\n  -DCMAKE_CXX_FLAGS=-std=c++11 \\\n  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/build/intermediates/cxx/Debug/603e6m32/obj/arm64-v8a \\\n  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/build/intermediates/cxx/Debug/603e6m32/obj/arm64-v8a \\\n  -DCMAKE_BUILD_TYPE=Debug \\\n  -B/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/.cxx/Debug/603e6m32/arm64-v8a \\\n  -GNinja \\\n  -DANDROID_STL=c++_shared\n", "file_": "/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/cpp/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -57769647}, {"level_": 0, "message_": "JSON generation completed with problem. Exception: com.android.ide.common.process.ProcessException: -- The C compiler identification is Clang 12.0.9\n-- The CXX compiler identification is Clang 12.0.9\n-- Detecting C compiler AB<PERSON> info\n-- Detecting C compiler AB<PERSON> info - done\n-- Check for working C compiler: /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/bin/clang - skipped\n-- Detecting C compile features\n-- Detecting C compile features - done\n-- Detecting CXX compiler ABI info\n-- Detecting CXX compiler ABI info - done\n-- Check for working CXX compiler: /opt/sdk/ndk/23.2.8568313/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ - skipped\n-- Detecting CXX compile features\n-- Detecting CXX compile features - done\n-- Configuring incomplete, errors occurred!\nSee also \"/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/.cxx/Debug/603e6m32/arm64-v8a/CMakeFiles/CMakeOutput.log\".\n\nC++ build system [configure] failed while executing:\n    /opt/sdk/cmake/3.22.1/bin/cmake \\\n      -H/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/cpp \\\n      -DCMAKE_SYSTEM_NAME=Android \\\n      -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \\\n      -DCMAKE_SYSTEM_VERSION=29 \\\n      -DANDROID_PLATFORM=android-29 \\\n      -DANDROID_ABI=arm64-v8a \\\n      -DCMAKE_ANDROID_ARCH_ABI=arm64-v8a \\\n      -DANDROID_NDK=/opt/sdk/ndk/23.2.8568313 \\\n      -DCMAKE_ANDROID_NDK=/opt/sdk/ndk/23.2.8568313 \\\n      -DCMAKE_TOOLCHAIN_FILE=/opt/sdk/ndk/23.2.8568313/build/cmake/android.toolchain.cmake \\\n      -DCMAKE_MAKE_PROGRAM=/opt/sdk/cmake/3.22.1/bin/ninja \\\n      -DCMAKE_CXX_FLAGS=-std=c++11 \\\n      -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/build/intermediates/cxx/Debug/603e6m32/obj/arm64-v8a \\\n      -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/build/intermediates/cxx/Debug/603e6m32/obj/arm64-v8a \\\n      -DCMAKE_BUILD_TYPE=Debug \\\n      -B/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/.cxx/Debug/603e6m32/arm64-v8a \\\n      -GNinja \\\n      -DANDROID_STL=c++_shared\n  from /media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app\nCMake Error at CMakeLists.txt:29 (add_subdirectory):\n  add_subdirectory given source\n  \"/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/cpp/../openssl/boringssl\"\n  which is not an existing directory.", "file_": "/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/cpp/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -410003028}]