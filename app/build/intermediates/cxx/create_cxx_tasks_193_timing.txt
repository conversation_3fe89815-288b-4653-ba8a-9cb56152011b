# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 17ms
    create-module-model completed in 19ms
    create-module-model
      create-cmake-model 17ms
    create-module-model completed in 19ms
    [gap of 10ms]
  create-initial-cxx-model completed in 59ms
create_cxx_tasks completed in 60ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 20ms
    create-module-model completed in 22ms
    create-module-model
      create-cmake-model 16ms
    create-module-model completed in 17ms
  create-initial-cxx-model completed in 55ms
create_cxx_tasks completed in 56ms

