# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 22ms
    create-module-model completed in 27ms
    create-module-model
      create-cmake-model 29ms
    create-module-model completed in 34ms
    [gap of 19ms]
  create-initial-cxx-model completed in 101ms
  [gap of 11ms]
create_cxx_tasks completed in 113ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 16ms
    create-module-model completed in 18ms
    create-module-model
      create-cmake-model 17ms
    create-module-model completed in 18ms
    [gap of 10ms]
  create-initial-cxx-model completed in 59ms
create_cxx_tasks completed in 67ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 16ms
    create-module-model completed in 19ms
    create-module-model
      create-cmake-model 15ms
    create-module-model completed in 18ms
    [gap of 12ms]
  create-initial-cxx-model completed in 59ms
create_cxx_tasks completed in 67ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 17ms
    create-module-model completed in 19ms
    create-module-model
      create-cmake-model 19ms
    create-module-model completed in 21ms
    [gap of 14ms]
  create-initial-cxx-model completed in 68ms
create_cxx_tasks completed in 76ms

