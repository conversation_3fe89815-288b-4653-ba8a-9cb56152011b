com.thundercomm.crown.fileencrypt.test.app-core-1.5.0-0 /home/<USER>/.gradle/caches/transforms-3/68f57515f307e72238dbbf2c94c76bf2/transformed/core-1.5.0/res
com.thundercomm.crown.fileencrypt.test.app-annotation-experimental-1.3.0-1 /home/<USER>/.gradle/caches/transforms-3/74bb2d5472718acc6fe2adc4e6f0c5ce/transformed/annotation-experimental-1.3.0/res
com.thundercomm.crown.fileencrypt.test.app-androidTest-2 /media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/build/generated/res/resValues/androidTest/debug
com.thundercomm.crown.fileencrypt.test.app-androidTest-3 /media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/build/generated/res/rs/androidTest/debug
com.thundercomm.crown.fileencrypt.test.app-mergeDebugAndroidTestResources-4 /media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/build/intermediates/incremental/debugAndroidTest/mergeDebugAndroidTestResources/merged.dir
com.thundercomm.crown.fileencrypt.test.app-mergeDebugAndroidTestResources-5 /media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/build/intermediates/incremental/debugAndroidTest/mergeDebugAndroidTestResources/stripped.dir
com.thundercomm.crown.fileencrypt.test.app-merged_res-6 /media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/build/intermediates/merged_res/debugAndroidTest
com.thundercomm.crown.fileencrypt.test.app-androidTest-7 /media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/androidTest/res
com.thundercomm.crown.fileencrypt.test.app-androidTestDebug-8 /media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/androidTestDebug/res
