com.thundercomm.crown.fileencrypt.app-material-1.9.0-0 /home/<USER>/.gradle/caches/transforms-3/058cfc4da2ae93637a16a1362856d4c0/transformed/material-1.9.0/res
com.thundercomm.crown.fileencrypt.app-lifecycle-runtime-2.5.1-1 /home/<USER>/.gradle/caches/transforms-3/05c84dba33780c7e01077a16068d7f3a/transformed/lifecycle-runtime-2.5.1/res
com.thundercomm.crown.fileencrypt.app-appcompat-resources-1.6.1-2 /home/<USER>/.gradle/caches/transforms-3/12d7cad622d5e92a50dacc8834f619a0/transformed/appcompat-resources-1.6.1/res
com.thundercomm.crown.fileencrypt.app-emoji2-1.2.0-3 /home/<USER>/.gradle/caches/transforms-3/135ca283e7cf3d338f7d6eff1b42490b/transformed/emoji2-1.2.0/res
com.thundercomm.crown.fileencrypt.app-fragment-1.3.6-4 /home/<USER>/.gradle/caches/transforms-3/293879b0575788d8256f25540990d4b4/transformed/fragment-1.3.6/res
com.thundercomm.crown.fileencrypt.app-lifecycle-viewmodel-2.5.1-5 /home/<USER>/.gradle/caches/transforms-3/30f39603487191cd39c3b1483fe54a0e/transformed/lifecycle-viewmodel-2.5.1/res
com.thundercomm.crown.fileencrypt.app-core-ktx-1.9.0-6 /home/<USER>/.gradle/caches/transforms-3/39446842ce32bb429bcb1c4b61904c2a/transformed/core-ktx-1.9.0/res
com.thundercomm.crown.fileencrypt.app-recyclerview-1.1.0-7 /home/<USER>/.gradle/caches/transforms-3/3f1089eaa24908819cb7fdfe19dac943/transformed/recyclerview-1.1.0/res
com.thundercomm.crown.fileencrypt.app-appcompat-1.6.1-8 /home/<USER>/.gradle/caches/transforms-3/452736d1d908cf94baf2f23bc48e1513/transformed/appcompat-1.6.1/res
com.thundercomm.crown.fileencrypt.app-cardview-1.0.0-9 /home/<USER>/.gradle/caches/transforms-3/47e93d947b3d3b5792658216ef936168/transformed/cardview-1.0.0/res
com.thundercomm.crown.fileencrypt.app-viewpager2-1.0.0-10 /home/<USER>/.gradle/caches/transforms-3/55b1b30c2cd642dff4825e542e10bc2d/transformed/viewpager2-1.0.0/res
com.thundercomm.crown.fileencrypt.app-constraintlayout-2.1.4-11 /home/<USER>/.gradle/caches/transforms-3/58f6eeac0fc84f13cba1d4f090550dd8/transformed/constraintlayout-2.1.4/res
com.thundercomm.crown.fileencrypt.app-core-1.9.0-12 /home/<USER>/.gradle/caches/transforms-3/6690daf909aed306e83f580bdb482290/transformed/core-1.9.0/res
com.thundercomm.crown.fileencrypt.app-savedstate-1.2.0-13 /home/<USER>/.gradle/caches/transforms-3/723e69cc8fd716adf353fea517505872/transformed/savedstate-1.2.0/res
com.thundercomm.crown.fileencrypt.app-annotation-experimental-1.3.0-14 /home/<USER>/.gradle/caches/transforms-3/74bb2d5472718acc6fe2adc4e6f0c5ce/transformed/annotation-experimental-1.3.0/res
com.thundercomm.crown.fileencrypt.app-lifecycle-viewmodel-savedstate-2.5.1-15 /home/<USER>/.gradle/caches/transforms-3/8b2adfe6b37326b3a9cb5ce21074671a/transformed/lifecycle-viewmodel-savedstate-2.5.1/res
com.thundercomm.crown.fileencrypt.app-transition-1.2.0-16 /home/<USER>/.gradle/caches/transforms-3/9ae651bc0be4ddc77ce8cb6ad4e65e60/transformed/transition-1.2.0/res
com.thundercomm.crown.fileencrypt.app-activity-1.6.0-17 /home/<USER>/.gradle/caches/transforms-3/a83a0f7737c4240db227f182fd3c5b7e/transformed/activity-1.6.0/res
com.thundercomm.crown.fileencrypt.app-startup-runtime-1.1.1-18 /home/<USER>/.gradle/caches/transforms-3/a89fbd729976d3997fa491032185eada/transformed/startup-runtime-1.1.1/res
com.thundercomm.crown.fileencrypt.app-lifecycle-livedata-core-2.5.1-19 /home/<USER>/.gradle/caches/transforms-3/ac985579ba2d485d7e72974c13f50802/transformed/lifecycle-livedata-core-2.5.1/res
com.thundercomm.crown.fileencrypt.app-lifecycle-process-2.4.1-20 /home/<USER>/.gradle/caches/transforms-3/db10c956bc7bde65dde29329887f7884/transformed/lifecycle-process-2.4.1/res
com.thundercomm.crown.fileencrypt.app-emoji2-views-helper-1.2.0-21 /home/<USER>/.gradle/caches/transforms-3/e17e41a3b28fac8146f5c5c54182168b/transformed/emoji2-views-helper-1.2.0/res
com.thundercomm.crown.fileencrypt.app-drawerlayout-1.1.1-22 /home/<USER>/.gradle/caches/transforms-3/e356bb9200c3a079164d966b8661b66d/transformed/drawerlayout-1.1.1/res
com.thundercomm.crown.fileencrypt.app-coordinatorlayout-1.1.0-23 /home/<USER>/.gradle/caches/transforms-3/fb07d92104a2da20f42a374c156a35e0/transformed/coordinatorlayout-1.1.0/res
com.thundercomm.crown.fileencrypt.app-pngs-24 /media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/build/generated/res/pngs/debug
com.thundercomm.crown.fileencrypt.app-resValues-25 /media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/build/generated/res/resValues/debug
com.thundercomm.crown.fileencrypt.app-rs-26 /media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/build/generated/res/rs/debug
com.thundercomm.crown.fileencrypt.app-packageDebugResources-27 /media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/build/intermediates/incremental/debug/packageDebugResources/merged.dir
com.thundercomm.crown.fileencrypt.app-packageDebugResources-28 /media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/build/intermediates/incremental/debug/packageDebugResources/stripped.dir
com.thundercomm.crown.fileencrypt.app-merged_res-29 /media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/build/intermediates/merged_res/debug
com.thundercomm.crown.fileencrypt.app-debug-30 /media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/debug/res
com.thundercomm.crown.fileencrypt.app-main-31 /media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/res
com.thundercomm.crown.fileencrypt.app-packaged_res-32 /media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/fileencryptinterface/build/intermediates/packaged_res/debug
