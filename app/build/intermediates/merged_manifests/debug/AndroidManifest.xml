<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.thundercomm.crown.fileencrypt"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="29"
        android:targetSdkVersion="33" />

    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <permission
        android:name="com.thundercomm.crown.fileencrypt.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.thundercomm.crown.fileencrypt.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:debuggable="true"
        android:directBootAware="true"
        android:extractNativeLibs="false"
        android:persistent="true"
        android:theme="@style/AppTheme" >
        <activity
            android:name="com.thundercomm.crown.fileencrypt.MainActivity"
            android:exported="true" >
        </activity>

        <service
            android:name="com.thundercomm.crown.fileencrypt.FileEncryptService"
            android:enabled="true"
            android:exported="true" >
            <intent-filter>
                <action android:name="com.thundercomm.crown.fileencrypt.IFileEncryptService" />
            </intent-filter>
        </service>

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.thundercomm.crown.fileencrypt.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
        </provider>
    </application>

</manifest>