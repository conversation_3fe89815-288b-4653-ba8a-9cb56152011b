1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.thundercomm.crown.fileencrypt"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="29"
8-->/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/AndroidManifest.xml
9        android:targetSdkVersion="33" />
9-->/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
11-->/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/AndroidManifest.xml:6:5-80
11-->/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/AndroidManifest.xml:6:22-77
12    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
12-->/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/AndroidManifest.xml:7:5-81
12-->/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/AndroidManifest.xml:7:22-78
13
14    <permission
14-->[androidx.core:core:1.9.0] /home/<USER>/.gradle/caches/transforms-3/6690daf909aed306e83f580bdb482290/transformed/core-1.9.0/AndroidManifest.xml:22:5-24:47
15        android:name="com.thundercomm.crown.fileencrypt.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
15-->[androidx.core:core:1.9.0] /home/<USER>/.gradle/caches/transforms-3/6690daf909aed306e83f580bdb482290/transformed/core-1.9.0/AndroidManifest.xml:23:9-81
16        android:protectionLevel="signature" />
16-->[androidx.core:core:1.9.0] /home/<USER>/.gradle/caches/transforms-3/6690daf909aed306e83f580bdb482290/transformed/core-1.9.0/AndroidManifest.xml:24:9-44
17
18    <uses-permission android:name="com.thundercomm.crown.fileencrypt.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
18-->[androidx.core:core:1.9.0] /home/<USER>/.gradle/caches/transforms-3/6690daf909aed306e83f580bdb482290/transformed/core-1.9.0/AndroidManifest.xml:26:5-97
18-->[androidx.core:core:1.9.0] /home/<USER>/.gradle/caches/transforms-3/6690daf909aed306e83f580bdb482290/transformed/core-1.9.0/AndroidManifest.xml:26:22-94
19
20    <application
20-->/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/AndroidManifest.xml:9:5-29:19
21        android:allowBackup="true"
21-->/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/AndroidManifest.xml:10:9-35
22        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
22-->[androidx.core:core:1.9.0] /home/<USER>/.gradle/caches/transforms-3/6690daf909aed306e83f580bdb482290/transformed/core-1.9.0/AndroidManifest.xml:28:18-86
23        android:debuggable="true"
24        android:directBootAware="true"
24-->/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/AndroidManifest.xml:12:9-39
25        android:extractNativeLibs="false"
26        android:persistent="true"
26-->/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/AndroidManifest.xml:11:9-34
27        android:theme="@style/AppTheme" >
27-->/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/AndroidManifest.xml:13:9-40
28        <activity
28-->/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/AndroidManifest.xml:15:9-18:20
29            android:name="com.thundercomm.crown.fileencrypt.MainActivity"
29-->/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/AndroidManifest.xml:16:13-41
30            android:exported="true" >
30-->/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/AndroidManifest.xml:17:13-36
31        </activity>
32
33        <service
33-->/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/AndroidManifest.xml:20:9-27:19
34            android:name="com.thundercomm.crown.fileencrypt.FileEncryptService"
34-->/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/AndroidManifest.xml:21:13-47
35            android:enabled="true"
35-->/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/AndroidManifest.xml:22:13-35
36            android:exported="true" >
36-->/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/AndroidManifest.xml:23:13-36
37            <intent-filter>
37-->/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/AndroidManifest.xml:24:13-26:29
38                <action android:name="com.thundercomm.crown.fileencrypt.IFileEncryptService" />
38-->/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/AndroidManifest.xml:25:17-96
38-->/media/ts/sdb1/work/1-crown/3-crown_3.2/3-qssi/QCM6490_apps_qssi13/LINUX/android/vendor/thundercomm/apps/FileEncrypt/app/src/main/AndroidManifest.xml:25:25-93
39            </intent-filter>
40        </service>
41
42        <provider
42-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/transforms-3/135ca283e7cf3d338f7d6eff1b42490b/transformed/emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
43            android:name="androidx.startup.InitializationProvider"
43-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/transforms-3/135ca283e7cf3d338f7d6eff1b42490b/transformed/emoji2-1.2.0/AndroidManifest.xml:25:13-67
44            android:authorities="com.thundercomm.crown.fileencrypt.androidx-startup"
44-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/transforms-3/135ca283e7cf3d338f7d6eff1b42490b/transformed/emoji2-1.2.0/AndroidManifest.xml:26:13-68
45            android:exported="false" >
45-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/transforms-3/135ca283e7cf3d338f7d6eff1b42490b/transformed/emoji2-1.2.0/AndroidManifest.xml:27:13-37
46            <meta-data
46-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/transforms-3/135ca283e7cf3d338f7d6eff1b42490b/transformed/emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
47                android:name="androidx.emoji2.text.EmojiCompatInitializer"
47-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/transforms-3/135ca283e7cf3d338f7d6eff1b42490b/transformed/emoji2-1.2.0/AndroidManifest.xml:30:17-75
48                android:value="androidx.startup" />
48-->[androidx.emoji2:emoji2:1.2.0] /home/<USER>/.gradle/caches/transforms-3/135ca283e7cf3d338f7d6eff1b42490b/transformed/emoji2-1.2.0/AndroidManifest.xml:31:17-49
49            <meta-data
49-->[androidx.lifecycle:lifecycle-process:2.4.1] /home/<USER>/.gradle/caches/transforms-3/db10c956bc7bde65dde29329887f7884/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:31:13-33:52
50                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
50-->[androidx.lifecycle:lifecycle-process:2.4.1] /home/<USER>/.gradle/caches/transforms-3/db10c956bc7bde65dde29329887f7884/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:32:17-78
51                android:value="androidx.startup" />
51-->[androidx.lifecycle:lifecycle-process:2.4.1] /home/<USER>/.gradle/caches/transforms-3/db10c956bc7bde65dde29329887f7884/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:33:17-49
52        </provider>
53    </application>
54
55</manifest>
