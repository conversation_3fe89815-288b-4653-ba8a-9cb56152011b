LOCAL_PATH := $(call my-dir)
MY_LOCAL_PATH := $(LOCAL_PATH)

# First include C++ dynamic library compilation
# ============================================================
include $(LOCAL_PATH)/app/src/main/cpp/Android.mk

# Restore top level path
LOCAL_PATH := $(MY_LOCAL_PATH)

# Compile APK application
# ============================================================
include $(CLEAR_VARS)

LOCAL_PACKAGE_NAME := FileEncrypt
LOCAL_MODULE_TAGS := optional

# Configuration for Android 11 (API 30)
LOCAL_SDK_VERSION := system_current

LOCAL_CERTIFICATE := platform
LOCAL_PRIVILEGED_MODULE := true

LOCAL_MANIFEST_FILE := app/src/main/AndroidManifest.xml

# Use correct relative path
LOCAL_SRC_FILES := \
    $(subst $(LOCAL_PATH)/,,$(shell find $(LOCAL_PATH)/app/src/main/java -name "*.java"))

# 添加对fileencrypt.api的依赖
LOCAL_STATIC_JAVA_LIBRARIES := fileencrypt.api

# Specify resource directory
LOCAL_RESOURCE_DIR := $(LOCAL_PATH)/app/src/main/res

# Ensure libfileencrypt.so is included in APK
LOCAL_JNI_SHARED_LIBRARIES := libfileencrypt

LOCAL_USE_AAPT2 := true
LOCAL_PROGUARD_FLAG_FILES := app/proguard-rules.pro
LOCAL_DEX_PREOPT := false

include $(BUILD_PACKAGE)