# Preserve the AIDL interface for consumers of the library
-keep class com.thundercomm.crown.fileencrypt.api.IFileEncryptServiceManager
-keep class com.thundercomm.crown.fileencrypt.api.IFileEncryptServiceManager$** { *; }
-keep class com.thundercomm.crown.fileencrypt.api.IFileEncryptServiceConnectCallback { *; }
-keep class com.thundercomm.crown.fileencrypt.api.FileEncryptServiceManager { *; }
-keep class com.thundercomm.crown.fileencrypt.api.constant.** { *; } 