plugins {
    id 'com.android.library'
}

android {
    namespace 'com.thundercomm.crown.fileencrypt.api'
    compileSdk 33

    defaultConfig {
        minSdk 29
        targetSdk 33

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.9.0'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}

tasks.register('makeJar', Copy) {
    from('build/intermediates/aar_main_jar/release/')
    into('out_libs/')
    include('classes.jar')
    rename('classes.jar', 'fileencrypt.api.jar')
}
makeJar.dependsOn(build) 