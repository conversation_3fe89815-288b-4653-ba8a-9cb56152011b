/*
* Copyright (C) 2025 THUNDERCOMM TECHNOLOGY Co.,Ltd.
* All Rights Reserved.
* Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
*/

package com.thundercomm.crown.fileencrypt.api;

import android.os.ParcelFileDescriptor;

interface IFileEncryptServiceManager {
    /**
     * Stream Encryption/Decryption Interface
     * @param isEncrypt true for encryption, false for decryption
     * @param inFd Input stream file descriptor
     * @param outFd Output stream file descriptor
     * @return true if successful, false otherwise
     */
    boolean processStream(in boolean isEncrypt, in ParcelFileDescriptor inFd, in ParcelFileDescriptor outFd);
} 