/*
* Copyright (C) 2025 THUNDERCOMM TECHNOLOGY Co.,Ltd.
* All Rights Reserved.
* Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
*/

package com.thundercomm.crown.fileencrypt.api;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.os.ParcelFileDescriptor;
import android.os.RemoteException;

/**
 * FileEncrypt Service Manager.
 * Manages connection and communication with FileEncryptService
 */
public class FileEncryptServiceManager {
    private static final String TAG = "FileEncryptServiceManager";

    private IFileEncryptServiceManager mService;
    private IFileEncryptServiceConnectCallback mConnectCallback;
    private boolean mBound = false;

    private static volatile FileEncryptServiceManager sInstance;

    /**
     * Get FileEncryptServiceManager instance
     * @return FileEncryptServiceManager
     */
    public static FileEncryptServiceManager getInstance() {
        if (sInstance == null) {
            synchronized (FileEncryptServiceManager.class) {
                if (sInstance == null) {
                    sInstance = new FileEncryptServiceManager();
                }
            }
        }
        return sInstance;
    }

    private FileEncryptServiceManager() {
    }

    private final ServiceConnection mConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName componentName, IBinder iBinder) {
            mService = IFileEncryptServiceManager.Stub.asInterface(iBinder);
            mBound = true;
            if (mConnectCallback != null) {
                mConnectCallback.onServiceConnected();
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName componentName) {
            mService = null;
            mBound = false;
            if (mConnectCallback != null) {
                mConnectCallback.onServiceDisconnected();
            }
        }
    };

    /**
     * Set service connection callback
     * @param callback Callback interface
     */
    public void setServiceConnectCallback(IFileEncryptServiceConnectCallback callback) {
        mConnectCallback = callback;
        if (mConnectCallback == null) {
            return;
        }
        if (mService != null) {
            mConnectCallback.onServiceConnected();
        } else {
            mConnectCallback.onServiceDisconnected();
        }
    }

    /**
     * Bind to the service.
     *
     * @param context Context to bind from
     * @return true if binding was initiated, false if service connection is already established
     */
    public boolean bindService(Context context) {
        if (mBound) {
            return false;
        }

        Intent intent = new Intent();
        intent.setComponent(new ComponentName(
                "com.thundercomm.crown.fileencrypt",
                "com.thundercomm.crown.fileencrypt.FileEncryptService"));

        return context.bindService(intent, mConnection, Context.BIND_AUTO_CREATE);
    }

    /**
     * Unbind FileEncryptService
     * @param context Context
     */
    public void unbindService(Context context) {
        context.unbindService(mConnection);
    }

    /**
     * Get service connection status
     * @return true if service is not connected
     */
    public boolean isServiceDisconnected() {
        return mService == null;
    }

    /**
     * Process data stream (encryption/decryption)
     * @param isEncrypt true for encryption, false for decryption
     * @param inFd Input file descriptor
     * @param outFd Output file descriptor
     * @return true if operation successful, false otherwise
     */
    public boolean processStream(boolean isEncrypt, ParcelFileDescriptor inFd, ParcelFileDescriptor outFd) {
        boolean result = false;
        try {
            if (mService == null) {
                return false;
            }
            result = mService.processStream(isEncrypt, inFd, outFd);
            return result;
        } catch (RemoteException exception) {
            exception.printStackTrace();
            return false;
        } finally {
            try {
                if (inFd != null) {
                    inFd.close();
                }
                if (outFd != null) {
                    outFd.close();
                }
            } catch (Exception exception) {
                exception.printStackTrace();
            }
        }
    }
}