/*
* Copyright (C) 2025 THUNDERCOMM TECHNOLOGY Co.,Ltd.
* All Rights Reserved.
* Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
*/

package com.thundercomm.crown.fileencrypt.api;

/**
 * Callback interface for FileEncryptService connection events
 */
public interface IFileEncryptServiceConnectCallback {
    /**
     * Called when service is successfully connected
     */
    void onServiceConnected();

    /**
     * Called when service is disconnected
     */
    void onServiceDisconnected();
}