/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package com.thundercomm.crown.fileencrypt.api;
public interface IFileEncryptServiceManager extends android.os.IInterface
{
  /** Default implementation for IFileEncryptServiceManager. */
  public static class Default implements com.thundercomm.crown.fileencrypt.api.IFileEncryptServiceManager
  {
    /**
         * Stream Encryption/Decryption Interface
         * @param isEncrypt true for encryption, false for decryption
         * @param inFd Input stream file descriptor
         * @param outFd Output stream file descriptor
         * @return true if successful, false otherwise
         */
    @Override public boolean processStream(boolean isEncrypt, android.os.ParcelFileDescriptor inFd, android.os.ParcelFileDescriptor outFd) throws android.os.RemoteException
    {
      return false;
    }
    @Override
    public android.os.IBinder asBinder() {
      return null;
    }
  }
  /** Local-side IPC implementation stub class. */
  public static abstract class Stub extends android.os.Binder implements com.thundercomm.crown.fileencrypt.api.IFileEncryptServiceManager
  {
    private static final java.lang.String DESCRIPTOR = "com.thundercomm.crown.fileencrypt.api.IFileEncryptServiceManager";
    /** Construct the stub at attach it to the interface. */
    public Stub()
    {
      this.attachInterface(this, DESCRIPTOR);
    }
    /**
     * Cast an IBinder object into an com.thundercomm.crown.fileencrypt.api.IFileEncryptServiceManager interface,
     * generating a proxy if needed.
     */
    public static com.thundercomm.crown.fileencrypt.api.IFileEncryptServiceManager asInterface(android.os.IBinder obj)
    {
      if ((obj==null)) {
        return null;
      }
      android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
      if (((iin!=null)&&(iin instanceof com.thundercomm.crown.fileencrypt.api.IFileEncryptServiceManager))) {
        return ((com.thundercomm.crown.fileencrypt.api.IFileEncryptServiceManager)iin);
      }
      return new com.thundercomm.crown.fileencrypt.api.IFileEncryptServiceManager.Stub.Proxy(obj);
    }
    @Override public android.os.IBinder asBinder()
    {
      return this;
    }
    @Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
    {
      java.lang.String descriptor = DESCRIPTOR;
      switch (code)
      {
        case INTERFACE_TRANSACTION:
        {
          reply.writeString(descriptor);
          return true;
        }
        case TRANSACTION_processStream:
        {
          data.enforceInterface(descriptor);
          boolean _arg0;
          _arg0 = (0!=data.readInt());
          android.os.ParcelFileDescriptor _arg1;
          if ((0!=data.readInt())) {
            _arg1 = android.os.ParcelFileDescriptor.CREATOR.createFromParcel(data);
          }
          else {
            _arg1 = null;
          }
          android.os.ParcelFileDescriptor _arg2;
          if ((0!=data.readInt())) {
            _arg2 = android.os.ParcelFileDescriptor.CREATOR.createFromParcel(data);
          }
          else {
            _arg2 = null;
          }
          boolean _result = this.processStream(_arg0, _arg1, _arg2);
          reply.writeNoException();
          reply.writeInt(((_result)?(1):(0)));
          return true;
        }
        default:
        {
          return super.onTransact(code, data, reply, flags);
        }
      }
    }
    private static class Proxy implements com.thundercomm.crown.fileencrypt.api.IFileEncryptServiceManager
    {
      private android.os.IBinder mRemote;
      Proxy(android.os.IBinder remote)
      {
        mRemote = remote;
      }
      @Override public android.os.IBinder asBinder()
      {
        return mRemote;
      }
      public java.lang.String getInterfaceDescriptor()
      {
        return DESCRIPTOR;
      }
      /**
           * Stream Encryption/Decryption Interface
           * @param isEncrypt true for encryption, false for decryption
           * @param inFd Input stream file descriptor
           * @param outFd Output stream file descriptor
           * @return true if successful, false otherwise
           */
      @Override public boolean processStream(boolean isEncrypt, android.os.ParcelFileDescriptor inFd, android.os.ParcelFileDescriptor outFd) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        boolean _result;
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeInt(((isEncrypt)?(1):(0)));
          if ((inFd!=null)) {
            _data.writeInt(1);
            inFd.writeToParcel(_data, 0);
          }
          else {
            _data.writeInt(0);
          }
          if ((outFd!=null)) {
            _data.writeInt(1);
            outFd.writeToParcel(_data, 0);
          }
          else {
            _data.writeInt(0);
          }
          boolean _status = mRemote.transact(Stub.TRANSACTION_processStream, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            return getDefaultImpl().processStream(isEncrypt, inFd, outFd);
          }
          _reply.readException();
          _result = (0!=_reply.readInt());
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
        return _result;
      }
      public static com.thundercomm.crown.fileencrypt.api.IFileEncryptServiceManager sDefaultImpl;
    }
    static final int TRANSACTION_processStream = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
    public static boolean setDefaultImpl(com.thundercomm.crown.fileencrypt.api.IFileEncryptServiceManager impl) {
      // Only one user of this interface can use this function
      // at a time. This is a heuristic to detect if two different
      // users in the same process use this function.
      if (Stub.Proxy.sDefaultImpl != null) {
        throw new IllegalStateException("setDefaultImpl() called twice");
      }
      if (impl != null) {
        Stub.Proxy.sDefaultImpl = impl;
        return true;
      }
      return false;
    }
    public static com.thundercomm.crown.fileencrypt.api.IFileEncryptServiceManager getDefaultImpl() {
      return Stub.Proxy.sDefaultImpl;
    }
  }
  /**
       * Stream Encryption/Decryption Interface
       * @param isEncrypt true for encryption, false for decryption
       * @param inFd Input stream file descriptor
       * @param outFd Output stream file descriptor
       * @return true if successful, false otherwise
       */
  public boolean processStream(boolean isEncrypt, android.os.ParcelFileDescriptor inFd, android.os.ParcelFileDescriptor outFd) throws android.os.RemoteException;
}
