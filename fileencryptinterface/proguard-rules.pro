# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Preserve the AIDL interface
-keep class com.thundercomm.crown.fileencrypt.api.IFileEncryptServiceManager
-keep class com.thundercomm.crown.fileencrypt.api.IFileEncryptServiceManager$** { *; }
-keep class com.thundercomm.crown.fileencrypt.api.IFileEncryptServiceConnectCallback { *; }
-keep class com.thundercomm.crown.fileencrypt.api.FileEncryptServiceManager { *; }
-keep class com.thundercomm.crown.fileencrypt.api.constant.** { *; } 