// Copyright (C) 2025 THUNDERCOMM TECHNOLOGY Co.,Ltd.
// All Rights Reserved.
// Confidential and Proprietary - Thundercomm Technology Co.,Ltd.

filegroup {
    name: "fileencrypt-server-interface-sources",
    srcs: [
        ":fileencrypt-server-interface-sources-aidl",
        ":fileencrypt-server-interface-sources-java",
    ],
}

filegroup {
    name: "fileencrypt-server-interface-sources-aidl",
    srcs: [
        "src/main/aidl/**/*.aidl",
    ],
    path: "src/main/aidl",
}

filegroup {
    name: "fileencrypt-server-interface-sources-java",
    srcs: [
        "src/main/java/**/*.java",
    ],
    path: "src/main/java",
}

java_library {
    name: "fileencrypt.api",
    installable: true,
    srcs: [
        ":fileencrypt-server-interface-sources",
    ],
} 